"use client";

import { AdminListTable } from "@/components/admin/AdminListTable";
import { AdminUserForm } from "@/components/admin/AdminUserForm";
import { RoleGuard } from "@/components/auth/RoleGuard";
import { ConfirmDialog } from "@/components/common/ConfirmDialog";
import { PageHeader } from "@/components/common/PageHeader";
import AppLayout from "@/layout/AppLayout";
import {
  clearAdminUsersError,
  deleteAdminUserRequest,
  fetchAdminUsersListRequest,
  setCurrentPageAdmin,
  setPageSizeAdmin,
  setSelectedAdminUser,
} from "@/store/adminUser/redux";
import {
  selectAdminUsersError,
  selectAdminUsersList,
  selectAdminUsersLoading,
  selectAdminUsersPagination,
  selectDeleteLoading,
  useCurrentUserRole
} from "@/store/adminUser/selector";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { Admin } from "@/types/adminUser";
import { navigate } from "@/utils/routerService";
import { showToast } from "@/utils/toast";
import {
  Add as AddIcon
} from "@mui/icons-material";
import {
  Alert,
  Box,
  Button,
  Container,
  Dialog,
  DialogContent,
  DialogTitle
} from "@mui/material";
import { useEffect, useState } from "react";

export default function AdminUsersPage() {
  const dispatch = useAppDispatch();

  // Redux state
  const adminUsersList = useAppSelector(selectAdminUsersList);
  const loading = useAppSelector(selectAdminUsersLoading);
  const error = useAppSelector(selectAdminUsersError);
  const deleteLoading = useAppSelector(selectDeleteLoading);
  const pagination = useAppSelector(selectAdminUsersPagination);

  // Local UI state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [adminUserToDelete, setAdminUserToDelete] = useState<Admin | null>(
    null
  );

  // Mock current user role - in real app, this would come from auth context
  const currentUserRole = useCurrentUserRole();

  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Organizations module for current user's role
  let adminPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const adminModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "admin_users"
      );
      if (adminModule) {
        adminPermissions = adminModule;
      }
    }
  }

  // Table state - now managed by Redux
  const [sortBy, setSortBy] = useState<string>("username");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // Filter state
  const [filters, setFilters] = useState({
    search: "",
    role: "all",
  });

  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchAdminUsersListRequest());
  }, [dispatch]);


  const handleSortChange = (
    newSortBy: string,
    newSortOrder: "asc" | "desc"
  ) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    dispatch(fetchAdminUsersListRequest());
  };

  const handlePageChange = (newPage: number) => {
    dispatch(setCurrentPageAdmin(newPage));
    dispatch(fetchAdminUsersListRequest());
  };

  const handlePageSizeChange = (newPageSize: number) => {
    dispatch(setPageSizeAdmin(newPageSize));
    dispatch(fetchAdminUsersListRequest());
  };

  const handleCreateAdminUser = async () => {
    setShowAddDialog(false);
  };

  const confirmDelete = async () => {
    if (!adminUserToDelete) return;

    try {
      const loadingToast = showToast.loading("Deleting admin user...");

      // Use Redux action for delete
      dispatch(deleteAdminUserRequest(adminUserToDelete.uuid));

      setTimeout(() => {
        showToast.dismiss(loadingToast);
        if (!deleteLoading) {
          showToast.success("Admin user deleted successfully!");
          setShowDeleteDialog(false);
          setAdminUserToDelete(null);
          // Data will be automatically updated via Redux
        }
      }, 1000);
    } catch (err: any) {
      showToast.error("Failed to delete admin user");
    }
  };

  const handleEdit = (user: Admin) => {
    // Set as selected admin user
    dispatch(setSelectedAdminUser(user));
    navigate(`/admin-users/${user.uuid}`);
  };

  const handleDelete = (user: Admin) => {
    setAdminUserToDelete(user);
    setShowDeleteDialog(true);
  };

  return (
    <RoleGuard requiredRole="super_admin" currentUserRole={currentUserRole}>
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            {/* Page Header */}
            <PageHeader
              title="Admin User Management"
              subtitle="Manage system administrators and their permissions"
              breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Admin Users" },
              ]}
              actions={
                <Button
                  variant="contained"
                  disabled={!adminPermissions.create}
                  startIcon={<AddIcon />}
                  onClick={() => navigate("/admin-users/create")}
                  sx={{ minWidth: 150 }}
                >
                  Add Admin User
                </Button>
              }
            />

            {error && (
              <Alert
                severity="error"
                sx={{ mb: 3 }}
                onClose={() => dispatch(clearAdminUsersError())}
              >
                {error}
              </Alert>
            )}

      
            <AdminListTable
              adminPermissions={adminPermissions}
              users={adminUsersList.map((user) => ({
                uuid: user.uuid,
                username: user.username,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                phone: user.phone,
                countrycode: user.countrycode,
                isactive: user.isactive || true,
                istemppassword: user.istemppassword || false,
                emailVerified: user.emailVerified || false,
                roles: Array.isArray(user.roles) ? user.roles : [user.roles],
                permissions: user.permissions || [],
                createdBy: user.createdBy,
                updatedBy: user.updatedBy,
                lastlogin: user.lastlogin,
                dateCreated: user.dateCreated,
                dateUpdated: user.dateUpdated,
                cognitoid: user.cognitoid,
              }))}
              loading={loading}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              page={pagination.currentPage}
              pageSize={pagination.pageSize}
              total={pagination.totalCount}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              onEdit={(user) =>
                handleEdit(adminUsersList.find((u) => u.uuid === user.uuid)!)
              }
              onDelete={(user) =>
                handleDelete(adminUsersList.find((u) => u.uuid === user.uuid)!)
              }
              currentUserRole={currentUserRole}
            />

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
              open={showDeleteDialog}
              title="Delete Admin User"
              message={`Are you sure you want to delete ${adminUserToDelete?.username} (${adminUserToDelete?.email})? This action cannot be undone.`}
              onConfirm={confirmDelete}
              onClose={() => {
                setShowDeleteDialog(false);
                setAdminUserToDelete(null);
              }}
            />

            {/* Add Admin User Dialog */}
            <Dialog
              open={showAddDialog}
              onClose={() => setShowAddDialog(false)}
              maxWidth="md"
              fullWidth
              PaperProps={{
                sx: {
                  minHeight: "80vh",
                  maxHeight: "90vh",
                },
              }}
            >
              <DialogTitle sx={{ pb: 1 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <AddIcon color="primary" />
                  Add New Admin User
                </Box>
              </DialogTitle>
              <DialogContent sx={{ pb: 0 }}>
                <AdminUserForm
                  currentUserRole={currentUserRole}
                  onSubmit={handleCreateAdminUser}
                  onCancel={() => setShowAddDialog(false)}
                />
              </DialogContent>
            </Dialog>
          </Box>
        </Container>
      </AppLayout>
    </RoleGuard>
  );
}
