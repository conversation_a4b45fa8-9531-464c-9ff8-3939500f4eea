import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Build query parameters
    const queryParams = new URLSearchParams();

    // Pagination
    const page = searchParams.get("page") || "1";
    const pageSize = searchParams.get("pageSize") || "10";
    queryParams.append("page", page);
    queryParams.append("pageSize", pageSize);

    // Sorting
    const sortBy = searchParams.get("sortBy");
    const sortOrder = searchParams.get("sortOrder");
    if (sortBy) queryParams.append("sortBy", sortBy);
    if (sortOrder) queryParams.append("sortOrder", sortOrder);

    // Filters
    const search = searchParams.get("search");
    const role = searchParams.get("role");
    if (search) queryParams.append("search", search);
    if (role && role !== "all") queryParams.append("role", role);

    const data = await callBackendApi(
      request,
      `/api/admin/admin-list?${queryParams.toString()}`
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching admin users list:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch admin users list",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
