import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    if (!body.memberUuids || !Array.isArray(body.memberUuids)) {
      return NextResponse.json(
        {
          error: "Invalid request body",
          message: "memberUuids array is required",
        },
        { status: 400 }
      );
    }

    if (body.memberUuids.length === 0) {
      return NextResponse.json(
        {
          error: "Invalid request body",
          message: "At least one member UUID is required",
        },
        { status: 400 }
      );
    }

    if (body.memberUuids.length > 500) {
      return NextResponse.json(
        {
          error: "Invalid request body",
          message: "Maximum 500 UUIDs allowed per request",
        },
        { status: 400 }
      );
    }

    const data = await callBackendApi(request, "/api/admin/bulk/delete", {
      method: "DELETE",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error performing bulk delete:", error);

    return NextResponse.json(
      {
        error: "Failed to perform bulk delete",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
