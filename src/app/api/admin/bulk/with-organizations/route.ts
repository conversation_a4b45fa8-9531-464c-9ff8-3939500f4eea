import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    // Get query parameters from the request
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    // Build the endpoint with query parameters
    const endpoint = queryString
      ? `/api/admin/bulk/with-organizations?${queryString}`
      : "/api/admin/bulk/with-organizations";

    const data = await callBackendApi(request, endpoint);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching members with organizations:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch members with organizations",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
