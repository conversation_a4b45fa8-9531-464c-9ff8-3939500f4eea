import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/admin/create-member", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error creating member:", error);

    return NextResponse.json(
      {
        error: "Failed to create member",
        message: error.message,
      },
      { status: 500 }
    );
  }
} 