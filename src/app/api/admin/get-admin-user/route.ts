import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackend<PERSON>pi(request, "/api/admin/get-admin-user");
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching admin user:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch admin user",
        message: error.message,
      },
      { status: 500 }
    );
  }
} 