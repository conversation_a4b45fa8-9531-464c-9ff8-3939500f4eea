import { NextRequest, NextResponse } from "next/server";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const { jobId } = await params;
    
    if (!jobId) {
      return NextResponse.json(
        { error: "Job ID is required" },
        { status: 400 }
      );
    }

    console.log("Deleting job ID:", jobId);

    // Get access token from request cookies
    let accessToken = null;

    // Try to get Cognito access token from cookies
    const cognitoAccessToken = request.cookies.get(
      "cognito_access_token"
    )?.value;
    if (
      cognitoAccessToken &&
      cognitoAccessToken.trim() !== "" &&
      cognitoAccessToken !== "undefined" &&
      cognitoAccessToken !== "null"
    ) {
      accessToken = cognitoAccessToken;
    } else {
      // Fallback to legacy token from cookies
      const legacyToken = request.cookies.get("access_token")?.value;
      if (
        legacyToken &&
        legacyToken.trim() !== "" &&
        legacyToken !== "undefined" &&
        legacyToken !== "null"
      ) {
        accessToken = legacyToken;
      }
    }

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      Accept: "application/json",
    };

    // Add authorization header if token exists
    if (accessToken) {
      requestHeaders.Authorization = `Bearer ${accessToken}`;
    }

    // Get backend URL from environment
    const backendUrl =
      process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

    console.log("Deleting job at:", `${backendUrl}/api/admin/members/export/jobs/${jobId}`);

    // Make fetch call to Python backend for job deletion
    const response = await fetch(`${backendUrl}/api/admin/members/export/jobs/${jobId}`, {
      method: "DELETE",
      headers: requestHeaders,
    });

    if (!response.ok) {
      let errorMessage = `Backend responded with status: ${response.status}`;
      let errorDetails = null;

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
        errorDetails = errorData;
        console.error("Backend job deletion error details:", errorData);
      } catch (parseError) {
        const errorText = await response.text();
        errorMessage = `${errorMessage} - ${errorText}`;
        errorDetails = errorText;
        console.error("Backend job deletion error (text):", errorText);
      }

      return NextResponse.json(
        {
          error: "Failed to delete export job",
          message: errorMessage,
          details: errorDetails,
          status: response.status,
        },
        { status: response.status }
      );
    }

    // Parse the deletion response
    const deleteData = await response.json();
    
    return NextResponse.json({
      success: true,
      message: deleteData.message || "Export job deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting export job:", error);
    return NextResponse.json(
      {
        error: "Failed to delete export job",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
