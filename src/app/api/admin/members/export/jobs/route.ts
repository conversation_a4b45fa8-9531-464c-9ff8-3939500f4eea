import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const pageSize = searchParams.get('page_size') || '10';
    const status = searchParams.get('status');

    console.log("Listing export jobs with params:", { page, pageSize, status });

    // Get access token from request cookies
    let accessToken = null;

    // Try to get Cognito access token from cookies
    const cognitoAccessToken = request.cookies.get(
      "cognito_access_token"
    )?.value;
    if (
      cognitoAccessToken &&
      cognitoAccessToken.trim() !== "" &&
      cognitoAccessToken !== "undefined" &&
      cognitoAccessToken !== "null"
    ) {
      accessToken = cognitoAccessToken;
    } else {
      // Fallback to legacy token from cookies
      const legacyToken = request.cookies.get("access_token")?.value;
      if (
        legacyToken &&
        legacyToken.trim() !== "" &&
        legacyToken !== "undefined" &&
        legacyToken !== "null"
      ) {
        accessToken = legacyToken;
      }
    }

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      Accept: "application/json",
    };

    // Add authorization header if token exists
    if (accessToken) {
      requestHeaders.Authorization = `Bearer ${accessToken}`;
    }

    // Get backend URL from environment
    const backendUrl =
      process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

    // Build query string
    const queryParams = new URLSearchParams();
    queryParams.append('page', page);
    queryParams.append('page_size', pageSize);
    if (status) {
      queryParams.append('status', status);
    }

    const url = `${backendUrl}/api/admin/members/export/jobs?${queryParams.toString()}`;
    console.log("Listing jobs at:", url);

    // Make fetch call to Python backend for job listing
    const response = await fetch(url, {
      method: "GET",
      headers: requestHeaders,
    });

    if (!response.ok) {
      let errorMessage = `Backend responded with status: ${response.status}`;
      let errorDetails = null;

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
        errorDetails = errorData;
        console.error("Backend jobs listing error details:", errorData);
      } catch (parseError) {
        const errorText = await response.text();
        errorMessage = `${errorMessage} - ${errorText}`;
        errorDetails = errorText;
        console.error("Backend jobs listing error (text):", errorText);
      }

      return NextResponse.json(
        {
          error: "Failed to list export jobs",
          message: errorMessage,
          details: errorDetails,
          status: response.status,
        },
        { status: response.status }
      );
    }

    // Parse the jobs response
    const jobsData = await response.json();
    
    return NextResponse.json({
      success: true,
      jobs: jobsData.jobs,
      pagination: jobsData.pagination,
    });
  } catch (error: any) {
    console.error("Error listing export jobs:", error);
    return NextResponse.json(
      {
        error: "Failed to list export jobs",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
