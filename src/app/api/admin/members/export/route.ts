import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("Export request body:", body);

    // Validate required fields
    if (
      !body.selectedFields ||
      !Array.isArray(body.selectedFields) ||
      body.selectedFields.length === 0
    ) {
      return NextResponse.json(
        { error: "selectedFields is required and must be a non-empty array" },
        { status: 422 }
      );
    }

    if (
      !body.notes ||
      typeof body.notes !== "string" ||
      body.notes.trim() === ""
    ) {
      return NextResponse.json(
        { error: "notes is required and must be a non-empty string" },
        { status: 422 }
      );
    }

    // Ensure filters is an object
    if (!body.filters || typeof body.filters !== "object") {
      body.filters = {};
    }

    console.log("Validated export request:", {
      filters: body.filters,
      selectedFields: body.selectedFields,
      notes: body.notes,
    });

    // Get access token from request cookies
    let accessToken = null;

    // Try to get Cognito access token from cookies
    const cognitoAccessToken = request.cookies.get(
      "cognito_access_token"
    )?.value;
    if (
      cognitoAccessToken &&
      cognitoAccessToken.trim() !== "" &&
      cognitoAccessToken !== "undefined" &&
      cognitoAccessToken !== "null"
    ) {
      accessToken = cognitoAccessToken;
    } else {
      // Fallback to legacy token from cookies
      const legacyToken = request.cookies.get("access_token")?.value;
      if (
        legacyToken &&
        legacyToken.trim() !== "" &&
        legacyToken !== "undefined" &&
        legacyToken !== "null"
      ) {
        accessToken = legacyToken;
      }
    }

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      "Content-Type": "application/json",
      Accept: "application/json",
    };

    // Add authorization header if token exists
    if (accessToken) {
      requestHeaders.Authorization = `Bearer ${accessToken}`;
    }

    // Get backend URL from environment
    const backendUrl =
      process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

    console.log("Backend URL:", `${backendUrl}/api/admin/members/export`);
    console.log("Request headers:", requestHeaders);
    console.log("Request body:", JSON.stringify(body, null, 2));

    // Make fetch call to Python backend for job-based response
    const response = await fetch(`${backendUrl}/api/admin/members/export`, {
      method: "POST",
      headers: requestHeaders,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      let errorMessage = `Backend responded with status: ${response.status}`;
      let errorDetails = null;

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
        errorDetails = errorData;
        console.error("Backend export error details:", errorData);
      } catch (parseError) {
        const errorText = await response.text();
        errorMessage = `${errorMessage} - ${errorText}`;
        errorDetails = errorText;
        console.error("Backend export error (text):", errorText);
      }

      return NextResponse.json(
        {
          error: "Failed to start export job",
          message: errorMessage,
          details: errorDetails,
          status: response.status,
        },
        { status: response.status }
      );
    }

    // Parse the job response
    const jobData = await response.json();
    
    return NextResponse.json({
      success: true,
      job_id: jobData.job_id,
      status: jobData.status,
      message: jobData.message,
      file_name: jobData.file_name,
      created_by: jobData.created_by,
    });
  } catch (error: any) {
    console.error("Error starting export job:", error);
    return NextResponse.json(
      {
        error: "Failed to start export job",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
