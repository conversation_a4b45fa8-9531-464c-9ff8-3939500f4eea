import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const { jobId } = await params;
    
    if (!jobId) {
      return NextResponse.json(
        { error: "Job ID is required" },
        { status: 400 }
      );
    }

    console.log("Checking status for job ID:", jobId);

    // Get access token from request cookies
    let accessToken = null;

    // Try to get Cognito access token from cookies
    const cognitoAccessToken = request.cookies.get(
      "cognito_access_token"
    )?.value;
    if (
      cognitoAccessToken &&
      cognitoAccessToken.trim() !== "" &&
      cognitoAccessToken !== "undefined" &&
      cognitoAccessToken !== "null"
    ) {
      accessToken = cognitoAccessToken;
    } else {
      // Fallback to legacy token from cookies
      const legacyToken = request.cookies.get("access_token")?.value;
      if (
        legacyToken &&
        legacyToken.trim() !== "" &&
        legacyToken !== "undefined" &&
        legacyToken !== "null"
      ) {
        accessToken = legacyToken;
      }
    }

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      Accept: "application/json",
    };

    // Add authorization header if token exists
    if (accessToken) {
      requestHeaders.Authorization = `Bearer ${accessToken}`;
    }

    // Get backend URL from environment
    const backendUrl =
      process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

    console.log("Checking job status at:", `${backendUrl}/api/admin/members/export/status/${jobId}`);

    // Make fetch call to Python backend for job status
    const response = await fetch(`${backendUrl}/api/admin/members/export/status/${jobId}`, {
      method: "GET",
      headers: requestHeaders,
    });

    if (!response.ok) {
      let errorMessage = `Backend responded with status: ${response.status}`;
      let errorDetails = null;

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
        errorDetails = errorData;
        console.error("Backend status check error details:", errorData);
      } catch (parseError) {
        const errorText = await response.text();
        errorMessage = `${errorMessage} - ${errorText}`;
        errorDetails = errorText;
        console.error("Backend status check error (text):", errorText);
      }

      return NextResponse.json(
        {
          error: "Failed to check job status",
          message: errorMessage,
          details: errorDetails,
          status: response.status,
        },
        { status: response.status }
      );
    }

    // Parse the status response
    const statusData = await response.json();
    
    return NextResponse.json({
      success: true,
      job_id: statusData.job_id,
      file_name: statusData.file_name,
      status: statusData.status,
      download_url: statusData.download_url,
      expires_at: statusData.expires_at,
      record_count: statusData.record_count,
      file_size: statusData.file_size,
      error_message: statusData.error_message,
      created_at: statusData.created_at,
      completed_at: statusData.completed_at,
    });
  } catch (error: any) {
    console.error("Error checking job status:", error);
    return NextResponse.json(
      {
        error: "Failed to check job status",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
