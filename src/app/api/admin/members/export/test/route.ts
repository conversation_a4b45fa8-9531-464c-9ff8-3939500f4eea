import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("Test export request body:", body);

    // Get access token from request cookies
    let accessToken = null;
    const cognitoAccessToken = request.cookies.get(
      "cognito_access_token"
    )?.value;
    if (
      cognitoAccessToken &&
      cognitoAccessToken.trim() !== "" &&
      cognitoAccessToken !== "undefined" &&
      cognitoAccessToken !== "null"
    ) {
      accessToken = cognitoAccessToken;
    } else {
      const legacyToken = request.cookies.get("access_token")?.value;
      if (
        legacyToken &&
        legacyToken.trim() !== "" &&
        legacyToken !== "undefined" &&
        legacyToken !== "null"
      ) {
        accessToken = legacyToken;
      }
    }

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      "Content-Type": "application/json",
      Accept: "application/json",
    };

    if (accessToken) {
      requestHeaders.Authorization = `Bearer ${accessToken}`;
    }

    const backendUrl =
      process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

    // Send a minimal test request
    const testBody = {
      filters: {},
      selectedFields: ["firstName", "lastName"],
      notes: "Test export",
    };

    console.log("Sending test request to backend...");
    console.log("URL:", `${backendUrl}/api/admin/members/export`);
    console.log("Headers:", requestHeaders);
    console.log("Body:", JSON.stringify(testBody, null, 2));

    const response = await fetch(`${backendUrl}/api/admin/members/export`, {
      method: "POST",
      headers: requestHeaders,
      body: JSON.stringify(testBody),
    });

    console.log("Backend response status:", response.status);
    console.log(
      "Backend response headers:",
      Object.fromEntries(response.headers.entries())
    );

    let responseText = "";
    try {
      responseText = await response.text();
      console.log("Backend response body:", responseText);
    } catch (error) {
      console.log("Could not read response body:", error);
    }

    return NextResponse.json({
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      body: responseText,
      testRequest: {
        url: `${backendUrl}/api/admin/members/export`,
        headers: requestHeaders,
        body: testBody,
      },
    });
  } catch (error: any) {
    console.error("Test export error:", error);
    return NextResponse.json(
      {
        error: "Test export failed",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
