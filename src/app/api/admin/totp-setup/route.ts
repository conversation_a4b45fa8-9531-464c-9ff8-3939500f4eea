import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackendApi(request, "/api/admin/totp-setup");
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error getting TOTP setup data:", error);

    return NextResponse.json(
      {
        error: "Failed to get TOTP setup data",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/admin/totp-setup", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error completing TOTP setup:", error);

    return NextResponse.json(
      {
        error: "Failed to complete TOTP setup",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
