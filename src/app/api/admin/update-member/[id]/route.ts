import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const data = await callBackendApi(
      request,
      `/api/admin/update-member/${id}`,
      {
        method: "PUT",
        body,
      }
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error updating member:", error);

    return NextResponse.json(
      {
        error: "Failed to update member",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
