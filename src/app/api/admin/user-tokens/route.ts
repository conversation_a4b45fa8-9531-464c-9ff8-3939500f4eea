import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    console.log("🔑 Getting user tokens...");
    
    // Call the backend to get user tokens
    const response = await callBackendApi(request, "/api/admin/user-tokens");
    
    console.log("✅ User tokens retrieved successfully");
    return NextResponse.json(response);
  } catch (error: any) {
    console.error("❌ Error getting user tokens:", error);
    return NextResponse.json(
      { 
        error: "Failed to get user tokens", 
        message: error.message 
      },
      { status: 500 }
    );
  }
} 