import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;

    const data = await callBackendApi(request, `/api/admin/${uuid}`);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching admin user:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch admin user",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;
    const body = await request.json();

    const data = await callBackendApi(request, `/api/admin/${uuid}`, {
      method: "PUT",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error updating admin user:", error);
    return NextResponse.json(
      {
        error: "Failed to update admin user",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;

    const data = await callBackendApi(request, `/api/admin/${uuid}`, {
      method: "DELETE",
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error deleting admin user:", error);
    return NextResponse.json(
      {
        error: "Failed to delete admin user",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
