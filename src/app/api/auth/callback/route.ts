import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    console.log("Auth0 Callback - Processing callback");

    // For now, just redirect to the member dashboard
    // In a real implementation, you would handle the Auth0 callback properly
    console.log("Auth0 Callback - Redirecting to dashboard");
    return NextResponse.redirect(
      new URL("/member-user/dashboard", request.url)
    );
  } catch (error) {
    console.error("Auth0 Callback - Error:", error);
    return NextResponse.redirect(new URL("/", request.url));
  }
}
