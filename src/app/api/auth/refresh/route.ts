import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/auth/refresh", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error refreshing auth token:", error);

    return NextResponse.json(
      {
        error: "Failed to refresh auth token",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
