import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const data = await callBackendApi(
      request,
      `/api/industries/${resolvedParams.id}`
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching industry:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch industry",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json();
    const resolvedParams = await params;
    const data = await callBackendApi(
      request,
      `/api/industries/${resolvedParams.id}`,
      {
        method: "PUT",
        body,
      }
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error updating industry:", error);

    return NextResponse.json(
      {
        error: "Failed to update industry",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const data = await callBackendApi(
      request,
      `/api/industries/${resolvedParams.id}`,
      {
        method: "DELETE",
      }
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error deleting industry:", error);

    return NextResponse.json(
      {
        error: "Failed to delete industry",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
