import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    // Get query parameters from the request
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    // Build the endpoint with query parameters
    const endpoint = queryString
      ? `/api/industries?${queryString}`
      : "/api/industries";

    const data = await callBackendApi(request, endpoint);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching industries:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch industries",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/industries", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error creating industry:", error);

    return NextResponse.json(
      {
        error: "Failed to create industry",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
