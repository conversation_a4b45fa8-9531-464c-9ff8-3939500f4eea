import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const resolvedParams = await params;
    const data = await callBackendApi(
      request,
      `/api/industries/uuid/${resolvedParams.uuid}`
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching industry by UUID:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch industry by UUID",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
