import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackendApi(request, "/api/logs/actions");

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching log actions:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch log actions",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
