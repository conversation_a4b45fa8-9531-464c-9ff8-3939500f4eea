import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackendApi(request, "/api/logs/export-fields");

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching export fields:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch export fields",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
