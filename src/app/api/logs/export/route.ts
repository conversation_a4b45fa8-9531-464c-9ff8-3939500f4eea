import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const data = await callBackendApi(request, "/api/logs/export", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error exporting logs:", error);
    return NextResponse.json(
      {
        error: "Failed to export logs",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
