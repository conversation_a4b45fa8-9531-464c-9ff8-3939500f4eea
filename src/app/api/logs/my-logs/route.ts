import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Build query parameters
    const queryParams = new URLSearchParams();
    
    // Timestamp filters
    const startTimestamp = searchParams.get("start_timestamp");
    const endTimestamp = searchParams.get("end_timestamp");
    if (startTimestamp) queryParams.append("start_timestamp", startTimestamp);
    if (endTimestamp) queryParams.append("end_timestamp", endTimestamp);
    
    // Action filters
    const purpose = searchParams.get("purpose");
    const action = searchParams.get("action");
    const actionExclude = searchParams.getAll("action_exclude");
    if (purpose) queryParams.append("purpose", purpose);
    if (action) queryParams.append("action", action);
    actionExclude.forEach(exclude => queryParams.append("action_exclude", exclude));
    
    // Pagination
    const page = searchParams.get("page");
    const pageSize = searchParams.get("page_size");
    if (page) queryParams.append("page", page);
    if (pageSize) queryParams.append("page_size", pageSize);
    
    // Sorting
    const sortBy = searchParams.get("sort_by") || "timestamp";
    const sortOrder = searchParams.get("sort_order") || "desc";
    queryParams.append("sort_by", sortBy);
    queryParams.append("sort_order", sortOrder);
    
    const data = await callBackendApi(
      request,
      `/api/logs/my-logs?${queryParams.toString()}`
    );
    
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching my logs:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch my logs",
        message: error.message,
      },
      { status: 500 }
    );
  }
} 