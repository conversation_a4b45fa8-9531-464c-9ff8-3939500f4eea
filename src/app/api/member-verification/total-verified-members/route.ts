import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackend<PERSON>pi(
      request,
      "/api/member-verification/total-verified-members"
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching total verified members:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch total verified members",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
