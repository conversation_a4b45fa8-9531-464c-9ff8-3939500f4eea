import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await callBackendApi(request, `/api/members/${id}`, {
      method: "DELETE",
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error deleting member:", error);

    return NextResponse.json(
      {
        error: "Failed to delete member",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
