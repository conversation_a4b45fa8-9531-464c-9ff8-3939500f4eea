import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const data = await callBackendApi(request, `/api/members/${id}/status`, {
      method: "PATCH",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error updating member status:", error);

    return NextResponse.json(
      {
        error: "Failed to update member status",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
