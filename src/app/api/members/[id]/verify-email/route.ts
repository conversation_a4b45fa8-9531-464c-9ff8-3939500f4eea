import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await callBackendApi(
      request,
      `/api/members/${id}/verify-email`,
      {
        method: "POST",
        body: {},
      }
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error verifying member email:", error);

    return NextResponse.json(
      {
        error: "Failed to verify member email",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
