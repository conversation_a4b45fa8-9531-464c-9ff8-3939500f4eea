import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const data = await callBackendApi(request, `/api/members/${id}/verify`, {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error verifying member:", error);

    return NextResponse.json(
      {
        error: "Failed to verify member",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
