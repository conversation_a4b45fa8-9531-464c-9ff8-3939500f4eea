import { callBackend<PERSON>pi } from "@/lib/apiUtils";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    // Call the backend API for authentication metrics
    const response = await callBackendApi(
      request,
      `/api/members/authentication-metrics`,
      {
        method: "GET",
      }
    );

    return NextResponse.json({
      message: "Authentication metrics retrieved successfully",
      data: response,
    });
  } catch (error) {
    console.error("Error fetching authentication metrics:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
} 