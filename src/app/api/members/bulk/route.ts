import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/members/bulk", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error performing bulk member action:", error);

    return NextResponse.json(
      {
        error: "Failed to perform bulk member action",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
