import { callBackendApi } from "@/lib/apiUtils";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { startDate, endDate } = body;

    // Validate required fields

    const response = await callBackendApi(
      request,
      `/api/members/metrics?startDate=${startDate}&endDate=${endDate}`,
      {
        method: "POST",
        body,
      }
    );

    return NextResponse.json({
      message: "Member metrics retrieved successfully",
      data: response,
    });
  } catch (error) {
    console.error("Error fetching member metrics:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}


