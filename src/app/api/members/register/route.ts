import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/members/register", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error registering member:", error);

    return NextResponse.json(
      {
        error: "Failed to register member",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
