import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackend<PERSON>pi(
      request,
      "/api/members/total-member-count"
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching total member count:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch total member count",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
