import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await callBackendApi(request, `/api/members/uuid/${id}`);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching member:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch member",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
