import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;
    // Get query parameters from the request
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    // Build the endpoint with query parameters
    const endpoint = queryString
      ? `/api/organizations/${uuid}/members?${queryString}`
      : `/api/organizations/${uuid}/members`;

    const data = await callBackendApi(request, endpoint);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching organization members:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch organization members",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
