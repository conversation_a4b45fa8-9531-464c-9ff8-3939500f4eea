import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;
    const data = await callBackendApi(request, `/api/organizations/${uuid}`);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching organization:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch organization",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;
    const body = await request.json();
    const data = await callBackendApi(request, `/api/organizations/${uuid}`, {
      method: "PUT",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error updating organization:", error);

    return NextResponse.json(
      {
        error: "Failed to update organization",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;
    const data = await callBackendApi(request, `/api/organizations/${uuid}`, {
      method: "DELETE",
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error deleting organization:", error);

    return NextResponse.json(
      {
        error: "Failed to delete organization",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
