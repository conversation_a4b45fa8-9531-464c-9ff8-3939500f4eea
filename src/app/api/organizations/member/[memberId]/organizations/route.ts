import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ memberId: string }> }
) {
  try {
    const { memberId } = await params;
    const data = await callBackendApi(
      request,
      `/api/organizations/member/${memberId}/organizations`
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching member organizations:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch member organizations",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
