import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/organizations/relations", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error creating organization relation:", error);

    return NextResponse.json(
      {
        error: "Failed to create organization relation",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(
      request,
      "/api/organizations/relations/",
      {
        method: "DELETE",
        body,
      }
    );

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error deleting organization relation:", error);

    return NextResponse.json(
      {
        error: "Failed to delete organization relation",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
