import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    // Get query parameters from the request
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    // Build the endpoint with query parameters
    const endpoint = queryString
      ? `/api/organizations?${queryString}`
      : "/api/organizations";

    const data = await callBackendApi(request, endpoint);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching organizations:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch organizations",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/organizations", {
      method: "POST",
      body,
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error creating organization:", error);

    return NextResponse.json(
      {
        error: "Failed to create organization",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
