import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackend<PERSON>pi(request, "/api/rbac/modules");
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching RBAC modules:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch RBAC modules",
        message: error.message,
      },
      { status: 500 }
    );
  }
} 