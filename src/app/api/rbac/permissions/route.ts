import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackend<PERSON>pi(request, "/api/rbac/permissions");
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching RBAC permissions:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch RBAC permissions",
        message: error.message,
      },
      { status: 500 }
    );
  }
} 