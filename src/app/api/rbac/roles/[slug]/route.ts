import { NextRequest, NextResponse } from "next/server";
import { callBackend<PERSON>pi } from "@/lib/apiUtils";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const data = await callBackendApi(request, `/api/rbac/roles/${slug}`, {
      method: "DELETE",
    });
    console.log("Delete role response:", data);

    // Return success response for DELETE operations
    return NextResponse.json({
      success: true,
      message: "Role deleted successfully",
      uuid: slug,
    });
  } catch (error: any) {
    console.error("Error deleting role:", error);
    return NextResponse.json(
      {
        error: "Failed to delete role",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
