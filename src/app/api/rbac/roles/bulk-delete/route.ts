import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/rbac/roles/bulk-delete", {
      method: "POST",
      body,
    });
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error bulk deleting roles:", error);
    return NextResponse.json(
      {
        error: "Failed to bulk delete roles",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
