import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function GET(request: NextRequest) {
  try {
    const data = await callBackend<PERSON>pi(request, "/api/rbac/roles");
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error fetching RBAC roles:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch RBAC roles",
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/rbac/roles", {
      method: "POST",
      body,
    });
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error creating RBAC role:", error);
    return NextResponse.json(
      {
        error: "Failed to create RBAC role",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
