import { NextRequest, NextResponse } from "next/server";
import { callBackendApi } from "@/lib/apiUtils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = await callBackendApi(request, "/api/rbac/roles/upsert-with-permissions", {
      method: "POST",
      body,
    });
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error upserting role with permissions:", error);
    return NextResponse.json(
      {
        error: "Failed to upsert role with permissions",
        message: error.message,
      },
      { status: 500 }
    );
  }
} 