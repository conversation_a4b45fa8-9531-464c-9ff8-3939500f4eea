"use client";

import {
  People as PeopleIcon,
  Person as PersonIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  VerifiedUser as VerifiedUserIcon,
  Star as StarIcon,
  Group as GroupIcon,
} from "@mui/icons-material";
import {
  Alert,
  Box,
  CircularProgress,
  Container,
  Grid,
  Typography,
  Collapse,
  Chip,
} from "@mui/material";
import { useEffect, useState } from "react";

import { StatCard } from "@/components/dashboard/StatCard";
import {
  DateRangeSelector,
  DateRange,
  getDateRangeUtils,
} from "@/components/dashboard/DateRangeSelector";
import RecentLogs from "@/components/dashboard/RecentLogs";
import AuthenticationMetricsCard from "@/components/dashboard/AuthenticationMetrics";
import AppLayout from "@/layout/AppLayout";
import {
  ActivityItem,
  dashboardService,
  DashboardStats,
  QuickAction,
} from "@/services/dashboard";
import { LogEntry } from "@/types/log";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { clearTempRegistrationCredentials } from "@/store/auth/redux";
import {
  fetchMemberMetricsRequest,
  selectMemberMetrics,
  selectMemberMetricsLoading,
  selectMemberMetricsError,
  fetchComparisonMetricsRequest,
  selectComparisonMetrics,
  selectComparisonMetricsLoading,
  selectComparisonMetricsError,
  fetchAuthenticationMetricsRequest,
  selectAuthenticationMetrics,
  selectAuthenticationMetricsLoading,
  selectAuthenticationMetricsError,
} from "@/store/members/redux";

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [quickActions, setQuickActions] = useState<QuickAction[]>([]);
  const [recentLogs, setRecentLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const {getCurrentMonthRange, getLastMonthRange} = getDateRangeUtils();
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange>({
    label: "This Month",
    ...getCurrentMonthRange()
  });

  // Comparison state
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonDateRange, setComparisonDateRange] = useState<DateRange>({
    label: "Last Month",
    ...getLastMonthRange()
  });

  const dispatch = useAppDispatch();

  // Redux selectors for member metrics
  const memberMetrics = useAppSelector(selectMemberMetrics);
  const metricsLoading = useAppSelector(selectMemberMetricsLoading);
  const metricsError = useAppSelector(selectMemberMetricsError);

  // Redux selectors for comparison metrics
  const comparisonMetrics = useAppSelector(selectComparisonMetrics);
  const comparisonMetricsLoading = useAppSelector(selectComparisonMetricsLoading);
  const comparisonMetricsError = useAppSelector(selectComparisonMetricsError);

  // Redux selectors for authentication metrics
  const authenticationMetrics = useAppSelector(selectAuthenticationMetrics);
  const authMetricsLoading = useAppSelector(selectAuthenticationMetricsLoading);
  const authMetricsError = useAppSelector(selectAuthenticationMetricsError);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        dispatch(clearTempRegistrationCredentials());

        // Fetch all dashboard data in parallel
        const [statsData, activitiesData, actionsData, logsData] =
          await Promise.all([
            dashboardService.getDashboardStats(),
            dashboardService.getRecentActivities(10),
            dashboardService.getQuickActions(),
            dashboardService.getRecentLogs(10),
          ]);

        setStats(statsData);
        setActivities(activitiesData);
        setQuickActions(actionsData);
        setRecentLogs(logsData);
      } catch (err: any) {
        console.error("Dashboard data fetch error:", err);
        // setError(err.message || "Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Fetch member metrics when date range changes
  useEffect(() => {
    // console.log("📅 Date range changed, dispatching metrics request:", {
    //   startDate: selectedDateRange.startDate,
    //   endDate: selectedDateRange.endDate,
    //   label: selectedDateRange.label,
    // });

    // For "All Time", don't send date parameters
    const requestPayload = selectedDateRange.label === "All Time" 
      ? {} 
      : {
          startDate: selectedDateRange.startDate!,
          endDate: selectedDateRange.endDate!,
        };

    dispatch(
      fetchMemberMetricsRequest(requestPayload)
    );
  }, [selectedDateRange, dispatch]);

  // Fetch comparison metrics when comparison date range changes
  useEffect(() => {
    if (showComparison) {
      // console.log("📅 Comparison date range changed, dispatching comparison metrics request:", {
      //   startDate: comparisonDateRange.startDate,
      //   endDate: comparisonDateRange.endDate,
      //   label: comparisonDateRange.label,
      // });

      // For "All Time", don't send date parameters
      const requestPayload = comparisonDateRange.label === "All Time" 
        ? {} 
        : {
            startDate: comparisonDateRange.startDate!,
            endDate: comparisonDateRange.endDate!,
          };

      dispatch(
        fetchComparisonMetricsRequest(requestPayload)
      );
    }
  }, [comparisonDateRange, showComparison, dispatch]);

  // Fetch authentication metrics on component mount
  useEffect(() => {
    // console.log("🔐 Fetching authentication metrics");
    dispatch(fetchAuthenticationMetricsRequest());
  }, [dispatch]);

  const handleDateRangeChange = (newRange: DateRange) => {
    console.log("🔄 Dashboard received date range change:", newRange);
    setSelectedDateRange(newRange);
  };

  const handleComparisonToggle = (show: boolean) => {
    console.log("🔄 Dashboard received comparison toggle:", show);
    setShowComparison(show);
  };

  const handleComparisonRangeChange = (newRange: DateRange) => {
    console.log("🔄 Dashboard received comparison range change:", newRange);
    setComparisonDateRange(newRange);
  };

  // Helper function to calculate percentage change
  // Returns positive when comparison period is higher than current period (showing historical strength)
  const calculatePercentageChange = (current: number, comparison: number): number => {
    if (comparison === 0) return current > 0 ? 100 : 0;
    return ((comparison - current) / comparison) * 100;
  };

  // Helper function to format percentage change
  const formatPercentageChange = (percentage: number): string => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(1)}%`;
  };

  // Helper function to get percentage change color
  // Green when comparison period was higher (showing historical strength)
  const getPercentageChangeColor = (percentage: number): "success" | "error" => {
    return percentage >= 0 ? "success" : "error";
  };

  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "60vh",
            }}
          >
            <CircularProgress size={60} />
          </Box>
        </Container>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          <Box
            sx={{
              mb: 4,
            }}
          >
            <Typography
              variant="h3"
              component="h1"
              sx={{
              color: "#1e3a8a",
              fontWeight: 700,
              fontSize: { xs: "1.75rem", sm: "2rem", md: "2.5rem" },
              textShadow: "0 1px 2px rgba(0,0,0,0.1)",
              mb: 2,
            }}
          >
            Dashboard
          </Typography>

            <DateRangeSelector
              selectedRange={selectedDateRange}
              onRangeChange={handleDateRangeChange}
              disabled={metricsLoading}
              showComparison={showComparison}
              onComparisonToggle={handleComparisonToggle}
              comparisonRange={comparisonDateRange}
              onComparisonRangeChange={handleComparisonRangeChange}
            />
          </Box>

          {/* Primary Member Metrics Cards */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
              Primary Metrics ({selectedDateRange.label})
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4} lg={2.4}>
                <StatCard
                  title="Total Members"
                  value={memberMetrics?.totalMembers || stats?.totalMembers || 0}
                  subtitle={`All registered members (${selectedDateRange.label})`}
                  icon={<PeopleIcon />}
                  color="primary"
                  loading={metricsLoading}
               
                />
              </Grid>

              <Grid item xs={12} sm={6} md={4} lg={2.4}>
                <StatCard
                  title="Verified Members"
                  value={stats?.verifiedMembers || 0}
                  subtitle="Members with verified status"
                  icon={<VerifiedUserIcon />}
                  color="success"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={4} lg={2.4}>
                <StatCard
                  title="Lite Members"
                  value={memberMetrics?.liteMembers || 0}
                  subtitle={`Lite tier members (${selectedDateRange.label})`}
                  icon={<PersonIcon />}
                  color="info"
                  loading={metricsLoading}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={4} lg={2.4}>
                <StatCard
                  title="Full Members"
                  value={memberMetrics?.fullMembers || 0}
                  subtitle={`Full tier members (${selectedDateRange.label})`}
                  icon={<StarIcon />}
                  color="warning"
                  loading={metricsLoading}

                />
              </Grid>

              <Grid item xs={12} sm={6} md={4} lg={2.4}>
                <StatCard
                  title="Active Members"
                  value={memberMetrics?.activeMembers || 0}
                  subtitle={`Active members (${selectedDateRange.label})`}
                  icon={<GroupIcon />}
                  color="secondary"
                  loading={metricsLoading}

                />
              </Grid>
            </Grid>
          </Box>

          {/* Comparison Member Metrics Cards */}
          <Collapse in={showComparison}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="h5" gutterBottom sx={{ color: 'secondary.main', fontWeight: 600 }}>
                Comparison Metrics ({comparisonDateRange.label})
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                  <StatCard
                    title="Total Members"
                    value={comparisonMetrics?.totalMembers || 0}
                    subtitle={`All registered members (${comparisonDateRange.label})`}
                    icon={<PeopleIcon />}
                    color="secondary"
                    loading={comparisonMetricsLoading}
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                  <StatCard
                    title="Verified Members"
                    value={stats?.verifiedMembers || 0}
                    subtitle="Members with verified status"
                    icon={<VerifiedUserIcon />}
                    color="secondary"
                    loading={comparisonMetricsLoading}
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                  <StatCard
                    title="Lite Members"
                    value={comparisonMetrics?.liteMembers || 0}
                    subtitle={`Lite tier members (${comparisonDateRange.label})`}
                    icon={<PersonIcon />}
                    color="secondary"
                    loading={comparisonMetricsLoading}
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                  <StatCard
                    title="Full Members"
                    value={comparisonMetrics?.fullMembers || 0}
                    subtitle={`Full tier members (${comparisonDateRange.label})`}
                    icon={<StarIcon />}
                    color="secondary"
                    loading={comparisonMetricsLoading}
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                  <StatCard
                    title="Active Members"
                    value={comparisonMetrics?.activeMembers || 0}
                    subtitle={`Active members (${comparisonDateRange.label})`}
                    icon={<GroupIcon />}
                    color="secondary"
                    loading={comparisonMetricsLoading}
                  />
                </Grid>
              </Grid>
            </Box>
          </Collapse>

          {/* Metrics Error Alerts */}
          {metricsError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {metricsError}
            </Alert>
          )}

          {comparisonMetricsError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              Comparison Error: {comparisonMetricsError}
            </Alert>
          )}

          {/* Authentication Metrics */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12}>
              <AuthenticationMetricsCard
                metrics={authenticationMetrics}
                loading={authMetricsLoading}
                error={authMetricsError}
              />
            </Grid>
          </Grid>

          {/* Recent Logs */}
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <RecentLogs logs={recentLogs} loading={loading} />
            </Grid>
          </Grid>

          {/* <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <ActivityFeed
                activities={activities}
                title="Recent Activity"
                maxItems={8}
              />
            </Grid>

            <Grid item xs={12} lg={4}>
              <QuickActions
                actions={quickActions}
                title="Quick Actions"
              />
            </Grid>
          </Grid> */}
        </Box>
      </Container>
    </AppLayout>
  );
}
