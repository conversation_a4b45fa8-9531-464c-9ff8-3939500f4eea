"use client";

import { PageLoading, useLoading } from "@/components/common";
import { ConfirmDialog } from "@/components/common/ConfirmDialog";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
import AppLayout from "@/layout/AppLayout";
import { membersService } from "@/services/members";
import { ExportJob } from "@/types/member";
import { showToast } from "@/utils/toast";
import { 
  isDownloadLinkExpired, 
  getTimeUntilExpiration, 
  isDownloadLinkExpiringSoon 
} from "@/utils/exportUtils";
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Container,
  IconButton,
  Pagination,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from "@mui/material";
import {
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  HourglassEmpty as HourglassEmptyIcon,
} from "@mui/icons-material";
import { useEffect, useState } from "react";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { useCurrentUserRole } from "@/store/adminUser/selector";

export default function CSVDownloadsPage() {
  const { setIsLoading, setLoadingMessage } = useLoading();

  const [jobs, setJobs] = useState<ExportJob[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalJobs, setTotalJobs] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<ExportJob | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Members module for current user's role
  let memberPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const memberModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "export_members"
      );
      if (memberModule) {
        memberPermissions = memberModule;
      }
    }
  }

  const isExport = memberPermissions.create || memberPermissions.update;
  // Load jobs on component mount and when filters change
  useEffect(() => {
    loadJobs();
  }, [currentPage, pageSize, statusFilter]);

  // // Show welcome message on first load
  // useEffect(() => {
  //   if (jobs.length === 0 && !loading) {
  //     showToast.info("Welcome to CSV Downloads! Here you can monitor and manage your export jobs.");
  //   }
  // }, [jobs.length, loading]);

  const loadJobs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await membersService.listExportJobs(
        currentPage,
        pageSize,
        statusFilter || undefined
      );
      
      // Check for newly completed jobs and show notifications
      const previousJobs = jobs;
      const newJobs = response.jobs;
      
      // Find newly completed jobs
      newJobs.forEach(newJob => {
        const previousJob = previousJobs.find(pJob => pJob.job_id === newJob.job_id);
        if (newJob.status === 'COMPLETED' && previousJob?.status === 'PROCESSING') {
          showToast.success(`Export completed: ${newJob.file_name} (${newJob.record_count} records)`);
        } else if (newJob.status === 'FAILED' && previousJob?.status === 'PROCESSING') {
          showToast.error(`Export failed: ${newJob.file_name} - ${newJob.error_message || 'Unknown error'}`);
        }
      });
      
      setJobs(response.jobs);
      setTotalPages(response.pagination.total_pages);
      setTotalJobs(response.pagination.total);
    } catch (err: any) {
      console.error("Failed to load export jobs:", err);
      setError(err.message || "Failed to load export jobs");
      showToast.error("Failed to load export jobs");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page);
  };

  const handleRefresh = () => {
    loadJobs();
  };

  const handleDownload = async (job: ExportJob) => {
    if (!job.download_url) {
      showToast.error("Download URL not available");
      return;
    }

    // Check if the download link has expired
    if (isDownloadLinkExpired(job.expires_at)) {
      showToast.error("Download link has expired. Please regenerate the export.");
      return;
    }

    try {
      // Create a temporary link to download the file
      const link = document.createElement("a");
      link.href = job.download_url;
      link.download = job.file_name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      showToast.success(`Download started: ${job.file_name}`);
    } catch (error) {
      console.error("Download failed:", error);
      showToast.error("Download failed");
    }
  };

  const handleDeleteClick = (job: ExportJob) => {
    setJobToDelete(job);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!jobToDelete) return;

    try {
      setDeleteLoading(true);
      await membersService.deleteExportJob(jobToDelete.job_id);
      
      setShowDeleteDialog(false);
      setJobToDelete(null);
      
      // Reload jobs to reflect the deletion
      await loadJobs();
      
      showToast.success(`Export job deleted: ${jobToDelete.file_name}`);
    } catch (err: any) {
      console.error("Failed to delete export job:", err);
      showToast.error("Failed to delete export job");
    } finally {
      setDeleteLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircleIcon color="success" />;
      case "FAILED":
        return <ErrorIcon color="error" />;
      case "PROCESSING":
        return <HourglassEmptyIcon color="warning" />;
      default:
        return <HourglassEmptyIcon color="disabled" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return "success";
      case "FAILED":
        return "error";
      case "PROCESSING":
        return "warning";
      default:
        return "default";
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "N/A";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i];
  };

  const formatDate = (dateString: string) => {
    // If the string has no timezone info, assume it's UTC
    const isUTC = /Z|[+-]\d{2}:\d{2}$/.test(dateString);
    const utcString = isUTC ? dateString : dateString + "Z";
  
    const date = new Date(utcString);
    return date.toLocaleString(undefined, {
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    });
  };
  
  

  // Show loading state
  if (loading && jobs.length === 0) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <PageLoading message="Loading export jobs..." />
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="xl">
        <ErrorBoundary>
          <Box sx={{ py: 3 }}>
            {/* Header */}
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
              <Typography variant="h4" component="h1" gutterBottom>
                CSV Export Jobs
              </Typography>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>

            {/* Filters */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                  <Typography variant="subtitle1" sx={{ minWidth: "fit-content" }}>
                    Filter by Status:
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 200 }}>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={statusFilter}
                      label="Status"
                      onChange={handleStatusFilterChange}
                    >
                      <MenuItem value="">All Statuses</MenuItem>
                      <MenuItem value="PROCESSING">Processing</MenuItem>
                      <MenuItem value="COMPLETED">Completed</MenuItem>
                      <MenuItem value="FAILED">Failed</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </CardContent>
            </Card>

            {/* Error Display */}
            {error && (
              <Card sx={{ mb: 3, bgcolor: "error.light" }}>
                <CardContent>
                  <Typography color="error" variant="body1">
                    {error}
                  </Typography>
                </CardContent>
              </Card>
            )}

            {/* Jobs Table */}
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Status</TableCell>
                    <TableCell>File Name</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Completed</TableCell>
                    <TableCell>Records</TableCell>
                    <TableCell>Expires</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {jobs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        <Typography variant="body1" color="text.secondary">
                          No export jobs found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    jobs.map((job) => (
                      <TableRow key={job.job_id}>
                        <TableCell>
                          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                            {getStatusIcon(job.status)}
                            <Chip
                              label={job.status}
                              color={getStatusColor(job.status) as any}
                              size="small"
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" noWrap>
                            {job.file_name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(job.created_at)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {job.completed_at ? formatDate(job.completed_at) : "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {job.record_count || "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography 
                            variant="body2" 
                            color={isDownloadLinkExpired(job.expires_at) ? "error.main" : 
                                   isDownloadLinkExpiringSoon(job.expires_at) ? "warning.main" : "text.secondary"}
                          >
                            {job.expires_at ? getTimeUntilExpiration(job.expires_at) : "No expiration"}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: "flex", gap: 1 }}>
                            {job.status === "COMPLETED" && job.download_url && (
                              <>
                                {isDownloadLinkExpired(job.expires_at) ? (
                                  <IconButton
                                    color="error"
                                    disabled
                                    title="Download link expired"
                                  >
                                    <ErrorIcon />
                                  </IconButton>
                                ) : (
                                  <IconButton
                                    color={isDownloadLinkExpiringSoon(job.expires_at) ? "warning" : "primary"}
                                    onClick={() => handleDownload(job)}
                                    disabled={!isExport}
                                    title={isDownloadLinkExpiringSoon(job.expires_at) 
                                      ? `Download (expires soon: ${getTimeUntilExpiration(job.expires_at)})`
                                      : "Download"
                                    }
                                  >
                                    <DownloadIcon />
                                  </IconButton>
                                )}
                              </>
                            )}

                            {job.status === "PROCESSING" && (
                              <IconButton
                                color="error"
                                title="Processing"
                              >
                                <HourglassEmptyIcon />
                              </IconButton>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Pagination */}
            {totalPages > 1 && (
              <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  color="primary"
                />
              </Box>
            )}

            {/* Summary */}
            <Box sx={{ mt: 3, textAlign: "center" }}>
              <Typography variant="body2" color="text.secondary">
                Showing {jobs.length} of {totalJobs} export jobs
              </Typography>
            </Box>
          </Box>

          {/* Delete Confirmation Dialog */}
          <ConfirmDialog
            open={showDeleteDialog}
            title="Delete Export Job"
            message={`Are you sure you want to delete the export job "${jobToDelete?.file_name}"? This action cannot be undone.`}
            onConfirm={confirmDelete}
            onClose={() => {
              setShowDeleteDialog(false);
              setJobToDelete(null);
            }}
            loading={deleteLoading}
            confirmText="Delete"
            cancelText="Cancel"
          />
        </ErrorBoundary>
      </Container>
    </AppLayout>
  );
}
