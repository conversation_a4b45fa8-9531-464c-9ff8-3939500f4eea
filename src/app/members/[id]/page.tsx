"use client";

import MemberDetailTabs from "@/components/members/MemberDetailTabs";
import MemberStatusChip from "@/components/members/MemberStatusChip";
import AppLayout from "@/layout/AppLayout";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchMemberRequest,
  selectCurrentMember,
  selectSingleMemberError,
  selectSingleMemberLoading,
} from "@/store/members/redux";
import {
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckIcon,
  Edit as EditIcon
} from "@mui/icons-material";
import {
  Alert,
  Avatar,
  Box,
  Breadcrumbs,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  Link,
  Typography
} from "@mui/material";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { getOrganizationsForMember } from "@/services/organizationService";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { selectAllRolesPermissions } from "@/store/roles/selector";

export default function MemberDetailPage() {
  const params = useParams();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const member_id = params.id as string;

  // Redux state
  const member = useAppSelector(selectCurrentMember);
  const loading = useAppSelector(selectSingleMemberLoading);
  const error = useAppSelector(selectSingleMemberError);
  const [active_tab, set_active_tab] = useState(0);

  // Local state for organization count to avoid full page reload
  const [organizationCount, setOrganizationCount] = useState(
    member?.organizations?.length || 0
  );

  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Members module for current user's role
  let memberPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const memberModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "members"
      );
      if (memberModule) {
        memberPermissions = memberModule;
      }
    }
  }

  useEffect(() => {
    if (member_id) {
      fetch_member();
    }
  }, [member_id]);

  // Update organization count when member data changes
  useEffect(() => {
    if (member?.organizations) {
      setOrganizationCount(member.organizations.length);
    }
  }, [member?.organizations]);

  const fetch_member = () => {
    dispatch(fetchMemberRequest(member_id));
  };

  const handle_tab_change = (new_tab: number) => {
    set_active_tab(new_tab);

    // Fetch tab-specific data when tab changes
    if (member) {
      const memberId = member.uuid;
      switch (new_tab) {
        case 1: // Organizations tab
          // Organizations tab handles its own data fetching
          break;
        // Awards and Feature Flags tabs removed - functionality not implemented
      }
    }
  };

  const handle_member_update = () => {
    fetch_member();
  };

  // Lightweight update function for organization operations
  const handle_organization_update = async () => {
    try {
      // Only update the organization count without full member reload
      const orgs = await getOrganizationsForMember(member_id);
      setOrganizationCount(orgs.length);
    } catch (error) {
      // Fallback to full member update if org count fetch fails
      console.warn(
        "Failed to update organization count, falling back to full member update"
      );
      fetch_member();
    }
  };

  const get_initials = (first_name?: string, last_name?: string) => {
    return `${first_name?.charAt(0) || ""}${
      last_name?.charAt(0) || ""
    }`.toUpperCase();
  };

  const format_date = (dateString: string) => {
    let normalizedDateString = dateString;

    // If it doesn't end with 'Z' or a timezone offset, assume it's UTC and append 'Z'
    if (!/[Z+-]/.test(dateString.slice(-1))) {
      // Remove fractional seconds if present
      normalizedDateString = dateString.split(".")[0] + "Z";
    }

    const utcDate = new Date(normalizedDateString);

    return utcDate.toLocaleString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true, // 12-hour format
    });
  };

  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Box
            sx={{
              py: 3,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "400px",
            }}
          >
            <CircularProgress size={60} />
          </Box>
        </Container>
      </AppLayout>
    );
  }

  if (error || !member) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            <Alert severity="error" sx={{ mb: 3 }}>
              {error || "Member not found"}
            </Alert>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => router.push("/members")}
            >
              Back to Members
            </Button>
          </Box>
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 3 }}>
            <Link
              color="inherit"
              href="/members"
              onClick={(e) => {
                e.preventDefault();
                router.push("/members");
              }}
              sx={{ cursor: "pointer" }}
            >
              Members
            </Link>{" "}
            <Typography color="text.primary">
              {member.firstName} {member.lastName}
            </Typography>
          </Breadcrumbs>
          {/* Header */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  mb: 2,
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 3 }}>
                  {" "}
                  <Avatar
                    sx={{
                      width: 80,
                      height: 80,
                      fontSize: "2rem",
                      bgcolor: "primary.main",
                    }}
                  >
                    {get_initials(member.firstName, member.lastName)}
                  </Avatar>
                  <Box>
                    <Typography
                      variant="h4"
                      sx={{ fontWeight: 700, color: "#1e3a8a" }}
                    >
                      {member.firstName} {member.lastName}
                    </Typography>
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{
                        mb: 1,
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        maxWidth: "200px",
                      }}
                    >
                      {member.professionalTitle || ""}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: "flex", gap: 1 }}>
                  {" "}
                  {/* <Tooltip title="Refresh">
                    <IconButton onClick={fetch_member} disabled={loading}>
                      <RefreshIcon />
                    </IconButton>
                  </Tooltip> */}
                  <Button
                    variant="outlined"
                    startIcon={<ArrowBackIcon />}
                    onClick={() => router.push("/members")}
                  >
                    Back to List
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<EditIcon />} 
                    disabled={!memberPermissions.update}
                    onClick={() => {
                      router.push(`/members/edit/${member_id}`);
                    }}
                  >
                    Edit Member
                  </Button>
                </Box>
              </Box>

              {/* Quick Info Grid */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 1 }}
                  >
                    {" "}
                    <Typography variant="body2" color="text.secondary">
                      <strong>Legacy ID:</strong>
                      <Box
                        sx={{
                          ml: 1,
                          display: "inline-block",
                          fontFamily: "monospace",
                          wordBreak: "break-all",
                        }}
                      >
                        {member.id}
                      </Box>
                      </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>UUID:</strong>
                      <Box
                        sx={{
                          ml: 1,
                          display: "inline-block",
                          fontFamily: "monospace",
                          wordBreak: "break-all",
                        }}
                      >
                        {member.uuid}
                      </Box>
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Email:</strong> {member.loginEmail}
                      {member.loginEmailVerified && (
                        <CheckIcon
                          sx={{
                            ml: 1,
                            fontSize: "1rem",
                            color: "success.main",
                          }}
                        />
                      )}
                    </Typography>
                    {member.personalBusinessEmail && (
                      <Typography variant="body2" color="text.secondary">
                        <strong>Personal Email:</strong>{" "}
                        {member.personalBusinessEmail}
                      </Typography>
                    )}
                    {member.phone && (
                      <Typography variant="body2" color="text.secondary">
                        <strong>Phone:</strong> {member.phone}
                      </Typography>
                    )}
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      component="span"
                    >
                      <strong>Identity Type:</strong>
                      <Box sx={{ ml: 1, display: "inline-block" }}>
                        <MemberStatusChip
                          type="identityType"
                          value={member.identityType || "unknown"}
                          size="small"
                          showIcon={false}
                        />
                      </Box>
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 1 }}
                  >
                    {" "}
                    <Typography variant="body2" color="text.secondary">
                      <strong>Created:</strong>{" "}
                      {format_date(member.dateCreated)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Last Updated:</strong>{" "}
                      {format_date(member.dateUpdated)}
                    </Typography>
                    {/* <Typography variant="body2" color="text.secondary">
                      <strong>Organizations:</strong> {organizationCount}
                    </Typography> */}
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
          {/* Tabs */}{" "}
          <MemberDetailTabs
            member={member}
            activeTab={active_tab}
            onTabChange={handle_tab_change}
            onMemberUpdate={handle_member_update}
            onOrganizationUpdate={handle_organization_update}
          />
        </Box>
      </Container>
    </AppLayout>
  );
}
