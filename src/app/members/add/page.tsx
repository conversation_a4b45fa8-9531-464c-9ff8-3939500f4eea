"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Breadcrumbs,
  Link,
  <PERSON>per,
  Step,
  StepLabel,
  Paper,
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  createMemberRequest,
  selectCreateMemberLoading,
  selectCreateMemberError,
  clearCreateMemberError,
} from "@/store/members/redux";
import { CreateMemberData } from "@/types/member";
import MemberForm from "@/components/members/MemberForm";
import AppLayout from "@/layout/AppLayout";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
import { convertMemberFormData } from "@/utils/memberUtils";

const steps = [
  { label: "Basic Information", description: "Personal and contact details" },
  {
    label: "Organization Details",
    description: "Company and business information",
  },
  {
    label: "Membership Settings",
    description: "Tier and status configuration",
  },
  { label: "Review & Create", description: "Final review and submission" },
];
const phoneRegExp = /^(\+1)? ?\(?\d{3}\)?[-.● ]?\d{3}[-.● ]?\d{4}$/;

export default function AddMemberPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();

  const loading = useAppSelector(selectCreateMemberLoading);
  const error = useAppSelector(selectCreateMemberError);

  const [activeStep, setActiveStep] = useState(0);
  const [selectedOrganizations, setSelectedOrganizations] = useState<any[]>([]);

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First name is required").max(50),
    lastName: Yup.string().required("Last name is required").max(50),
    loginEmail: Yup.string()
      .email("Invalid email")
      .required("email is required"),
    phone: Yup.string()
      .matches(phoneRegExp, "Phone number is not valid")
      .optional(),
    personalBusinessEmail: Yup.string().email("Invalid email").optional(),
    professionalTitle: Yup.string().max(100).optional(),

    membershipTier: Yup.mixed<"lite" | "premium">()
      .oneOf(["lite", "premium"])
      .required("Membership tier is required"),
    communityStatus: Yup.string()
      .oneOf(["unverified", "verified", "pending", "rejected"])
      .required("Community status is required"),
    password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .max(100, "Password must be less than 100 characters")
      .matches(
        /^(?=.*[a-z])/,
        "Password must contain at least one lowercase letter"
      )
      .matches(
        /^(?=.*[A-Z])/,
        "Password must contain at least one uppercase letter"
      )
      .matches(/^(?=.*\d)/, "Password must contain at least one number")
      .matches(
        /^(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/,
        "Password must contain at least one special character"
      )
      .test("no-spaces", "Password cannot contain spaces", (val) =>
        val ? !/\s/.test(val) : true
      )
      .required("Password is required"),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref("password")], "Passwords must match")
      .required("Please confirm your password"),
    // Organization fields
    organization: Yup.object({
      name: Yup.string().optional(),
      address1: Yup.string().optional(),
      address2: Yup.string().optional(),
      city: Yup.string().optional(),
      state: Yup.string().optional(),
      zip: Yup.string().optional(),
      phone: Yup.string().optional(),
      email: Yup.string().email("Invalid email").optional(),
      annualRevenue: Yup.string().optional(),
      industry: Yup.string().optional(),
      yearFounded: Yup.string().optional(),
      companySize: Yup.string().optional(),
    }).optional(),
  });

  const initialValues: CreateMemberData & { selectedOrganizations?: any[] } = {
    firstName: "",
    lastName: "",
    loginEmail: "",
    phone: "",
    personalBusinessEmail: "",
    professionalTitle: "",

    membershipTier: "lite",
    communityStatus: "unverified",
    password: "",
    confirmPassword: "",
    selectedOrganizations: [],
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      dispatch(clearCreateMemberError());
      // Convert camelCase to snake_case for API submission
      const convertedValues = { ...values, selectedOrganizations };
      dispatch(createMemberRequest(convertedValues));
    },
    enableReinitialize: true,
  });


  const handleNext = () => {
    // Validate current step before proceeding
    const currentStepFields = getCurrentStepFields(activeStep);
    currentStepFields.forEach((field) => {
      formik.setFieldTouched(field, true, false);
    });

    // Force validation for the current step
    formik.validateForm().then((errors) => {
      console.log("Validation errors:", errors);
      console.log("Current step:", activeStep);
      console.log("Can proceed:", canProceed);
      console.log("Form values:", formik.values);

      // Check if there are any errors for the current step fields
      const hasStepErrors = currentStepFields.some((field) => {
        if (field.includes(".")) {
          const [parent, child] = field.split(".");
          return (errors as any)[parent] && (errors as any)[parent]?.[child];
        }
        return (errors as any)[field];
      });

      // Additional validation for step 0 (password requirements)
      let canProceedToNext = !hasStepErrors && canProceed;

      if (activeStep === 0) {
        const password = formik.values.password;
        const confirmPassword = formik.values.confirmPassword;

        // Check password requirements
        const passwordValid =
          password &&
          password.length >= 8 &&
          /[a-z]/.test(password) &&
          /[A-Z]/.test(password) &&
          /\d/.test(password) &&
          /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password) &&
          !/\s/.test(password);

        // Check if passwords match
        const passwordsMatch = password === confirmPassword;

        canProceedToNext = canProceedToNext && passwordValid && passwordsMatch;

        if (!passwordValid) {
          console.log("Password does not meet requirements");
        }
        if (!passwordsMatch) {
          console.log("Passwords do not match");
        }
      }

      if (canProceedToNext) {
        setActiveStep((prevStep) => prevStep + 1);
      } else {
        console.log("Cannot proceed - validation failed");
      }
    });
  };

  const getCurrentStepFields = (step: number) => {
    switch (step) {
      case 0:
        return [
          "firstName",
          "lastName",
          "loginEmail",
          "password",
          "confirmPassword",
        ];
      case 1:
        return [];
      case 2:
        return ["membershipTier", "communityStatus"];
      default:
        return [];
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleCancel = () => {
    router.push("/members");
  };

  const handleOrganizationSelectionChange = (organizations: any[]) => {
    setSelectedOrganizations(organizations);
  };

  const handleSubmit = () => {
    if (formik.isValid) {
      formik.handleSubmit();
    }
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 0: // Basic Information
        // Check if all required fields have values
        const basicFieldsValid =
          formik.values.firstName?.trim() &&
          formik.values.lastName?.trim() &&
          formik.values.loginEmail?.trim() &&
          formik.values.password?.trim() &&
          formik.values.confirmPassword?.trim();

        // Check if passwords match
        const passwordsMatch =
          formik.values.password === formik.values.confirmPassword;

        // Check if password meets requirements (basic check)
        const passwordMeetsRequirements =
          formik.values.password &&
          formik.values.password.length >= 8 &&
          /[a-z]/.test(formik.values.password) &&
          /[A-Z]/.test(formik.values.password) &&
          /\d/.test(formik.values.password) &&
          /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(
            formik.values.password
          ) &&
          !/\s/.test(formik.values.password);

        // Check for validation errors
        const basicFieldsNoErrors =
          !formik.errors.firstName &&
          !formik.errors.lastName &&
          !formik.errors.loginEmail &&
          !formik.errors.password &&
          !formik.errors.confirmPassword;

        return (
          basicFieldsValid &&
          basicFieldsNoErrors &&
          passwordsMatch &&
          passwordMeetsRequirements
        );

      case 1: // Organization Details
        // Organization step is always valid since identityType is removed
        return true;

      case 2: // Membership Settings
        const membershipFieldsValid =
          formik.values.membershipTier &&
          formik.values.communityStatus;

        const membershipFieldsNoErrors =
          !formik.errors.membershipTier &&
          !formik.errors.communityStatus;

        return membershipFieldsValid && membershipFieldsNoErrors;

      case 3: // Review
        return formik.isValid;

      default:
        return false;
    }
  };

  const canProceed = isStepValid(activeStep);

  // Debug function to help identify validation issues
  const getValidationDebugInfo = () => {
    const errors = Object.keys(formik.errors).length > 0 ? formik.errors : null;
    const touched =
      Object.keys(formik.touched).length > 0 ? formik.touched : null;
    return { errors, touched, isValid: formik.isValid, canProceed };
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Enter the member's basic personal and contact information.
            </Typography>
            <MemberForm
              formik={formik}
              showBasicFields={true}
              showOrganizationFields={false}
              showMembershipFields={false}
              showOrganizationLinking={false}
            />
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Organization Details
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Associate this member with organizations or create a new
              organization.
            </Typography>
            <MemberForm
              formik={formik}
              showBasicFields={false}
              showOrganizationFields={true}
              showMembershipFields={false}
              showOrganizationLinking={true}
              selectedOrganizations={selectedOrganizations}
              onOrganizationSelectionChange={handleOrganizationSelectionChange}
            />
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Membership Settings
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Configure the member's membership tier and status.
            </Typography>
            <MemberForm
              formik={formik}
              showBasicFields={false}
              showOrganizationFields={false}
              showMembershipFields={true}
              showOrganizationLinking={false}
            />
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review & Create
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Review all information before creating the member.
            </Typography>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Basic Information
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Name:</strong> {formik.values.firstName}{" "}
                  {formik.values.lastName}
                </Typography>
                <Typography variant="body2">
                  <strong>email:</strong> {formik.values.loginEmail}
                </Typography>
                {formik.values.phone && (
                  <Typography variant="body2">
                    <strong>Phone:</strong> {formik.values.phone}
                  </Typography>
                )}
                {formik.values.professionalTitle && (
                  <Typography variant="body2">
                    <strong>Title:</strong> {formik.values.professionalTitle}
                  </Typography>
                )}
                <Typography variant="body2">
                  <strong>Password:</strong> {"•".repeat(8)} (hidden for
                  security)
                </Typography>
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                Organization Information
              </Typography>
              <Box sx={{ mb: 2 }}>

                {selectedOrganizations.length > 0 && (
                  <>
                    <Typography variant="subtitle1" gutterBottom>
                      Associated Organizations
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      {selectedOrganizations.map((org, index) => (
                        <Typography
                          key={org.uuid}
                          variant="body2"
                          sx={{ ml: 2 }}
                        >
                          • {org.name}
                          {org.city && org.state
                            ? ` (${org.city}, ${org.state})`
                            : org.industry
                            ? ` (${org.industry})`
                            : ""}
                        </Typography>
                      ))}
                    </Box>
                  </>
                )}
                {formik.values.organization?.name && (
                  <Typography variant="body2">
                    <strong>New Organization:</strong>{" "}
                    {formik.values.organization.name}
                  </Typography>
                )}
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                Membership
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Membership Tier:</strong>{" "}
                  {formik.values.membershipTier}
                </Typography>
                <Typography variant="body2">
                  <strong>Community Status:</strong>{" "}
                  {formik.values.communityStatus}
                </Typography>
              </Box>
            </Paper>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <ErrorBoundary isPageLevel={true}>
      <AppLayout>
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Breadcrumbs */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link
                color="inherit"
                href="/members"
                onClick={(e) => {
                  e.preventDefault();
                  router.push("/members");
                }}
                sx={{ cursor: "pointer" }}
              >
                Members
              </Link>
              <Typography color="text.primary">Add Member</Typography>
            </Breadcrumbs>

            {/* Header */}
            <Box sx={{ mb: 4 }}>
              <Typography
                variant="h3"
                component="h1"
                gutterBottom
                sx={{
                  color: "#1e3a8a",
                  fontWeight: 700,
                  fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2.5rem" },
                }}
              >
                Add New Member
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{
                  fontSize: { xs: "0.875rem", md: "1rem" },
                }}
              >
                Create a new member account with comprehensive information
              </Typography>
            </Box>

            {/* Stepper */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Stepper activeStep={activeStep} alternativeLabel>
                  {steps.map((step, index) => (
                    <Step key={step.label}>
                      <StepLabel>
                        <Box>
                          <Typography variant="subtitle2">
                            {step.label}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {step.description}
                          </Typography>
                        </Box>
                      </StepLabel>
                    </Step>
                  ))}
                </Stepper>
              </CardContent>
            </Card>

            {/* Error Alert - only for validation errors now, saga handles API errors */}
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {/* Form Content */}
            <Card>
              <CardContent sx={{ p: { xs: 2, md: 3 } }}>
                <Box>
                  {renderStepContent(activeStep)}

                  {/* Action Buttons */}
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mt: 4,
                      gap: 2,
                    }}
                  >
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Button
                        variant="outlined"
                        startIcon={<ArrowBackIcon />}
                        onClick={handleCancel}
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                      {activeStep > 0 && (
                        <Button
                          variant="outlined"
                          onClick={handleBack}
                          disabled={loading}
                        >
                          Back
                        </Button>
                      )}
                    </Box>

                    <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
                      {activeStep < steps.length - 1 ? (
                        <>
                          <Button
                            variant="contained"
                            onClick={handleNext}
                            disabled={!canProceed || loading}
                          >
                            Next
                          </Button>
                          {!canProceed && !loading && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{ ml: 1 }}
                            >
                              {activeStep === 0 &&
                              formik.values.password &&
                              (!/[a-z]/.test(formik.values.password) ||
                                !/[A-Z]/.test(formik.values.password) ||
                                !/\d/.test(formik.values.password) ||
                                !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(
                                  formik.values.password
                                ) ||
                                /\s/.test(formik.values.password))
                                ? "Password must meet all requirements"
                                : activeStep === 0 &&
                                  formik.values.password &&
                                  formik.values.confirmPassword &&
                                  formik.values.password !==
                                    formik.values.confirmPassword
                                ? "Passwords do not match"
                                : "Please complete all required fields"}
                            </Typography>
                          )}
                        </>
                      ) : (
                        <>
                          <Button
                            variant="contained"
                            startIcon={
                              loading ? (
                                <CircularProgress size={20} />
                              ) : (
                                <SaveIcon />
                              )
                            }
                            onClick={handleSubmit}
                            disabled={!formik.isValid || loading}
                          >
                            {loading ? "Creating..." : "Create Member"}
                          </Button>
                          {!formik.isValid && !loading && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{ ml: 1 }}
                            >
                              Please fix all validation errors
                            </Typography>
                          )}
                        </>
                      )}
                    </Box>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </AppLayout>
    </ErrorBoundary>
  );
}
