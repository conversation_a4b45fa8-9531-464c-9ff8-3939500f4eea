"use client";

import React, { useState, useEffect, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Breadcrumbs,
  Link,
  Stepper,
  Step,
  StepLabel,
  Paper,
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchMemberRequest,
  updateMemberRequest,
  selectCurrentMember,
  selectSingleMemberLoading,
  selectSingleMemberError,
  selectUpdateMemberLoading,
  selectUpdateMemberError,
  clearSingleMemberError,
  clearUpdateMemberError,
} from "@/store/members/redux";
import { UpdateMemberData } from "@/types/member";
import MemberForm from "@/components/members/MemberForm";
import AppLayout from "@/layout/AppLayout";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
import { convertMemberFormData } from "@/utils/memberUtils";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { showToast } from "@/utils/toast";

const steps = [
  { label: "Basic Information", description: "Personal and contact details" },
  {
    label: "Organization Details",
    description: "Company and business information",
  },
  {
    label: "Membership Settings",
    description: "Tier and status configuration",
  },
  { label: "Review & Update", description: "Final review and submission" },
];

export default function EditMemberPage() {
  const params = useParams();
  const router = useRouter();
  const dispatch = useAppDispatch();

  const memberId = params.id as string;

  const member = useAppSelector(selectCurrentMember);
  const loading = useAppSelector(selectSingleMemberLoading);
  const error = useAppSelector(selectSingleMemberError);
  const updateLoading = useAppSelector(selectUpdateMemberLoading);
  const updateError = useAppSelector(selectUpdateMemberError);

  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Members module for current user's role
  let memberPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const memberModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "members"
      );
      if (memberModule) {
        memberPermissions = memberModule;
      }
    }
  }

  // Block access if user does not have update permission
  useEffect(() => {
    if (!memberPermissions.update) {
      router.replace(`/members/${memberId}?error=permission`);
    }
  }, [memberPermissions.update, router, memberId]);

  const [activeStep, setActiveStep] = useState(0);
  const [selectedOrganizations, setSelectedOrganizations] = useState<any[]>([]);

  // Add ref to track if organizations have been set from member data
  const hasSetOrganizationsFromMember = useRef(false);

  useEffect(() => {
    if (memberId) {
      dispatch(clearSingleMemberError());
      dispatch(fetchMemberRequest(memberId));
    }
  }, [memberId, dispatch]);

  // Update selected organizations when member data changes
  useEffect(() => {
    console.log("🔍 DEBUG EditMember: Member data changed:", member?.uuid);
    console.log(
      "🔍 DEBUG EditMember: Member organizations:",
      member?.organizations
    );
    console.log(
      "🔍 DEBUG EditMember: hasSetOrganizationsFromMember.current:",
      hasSetOrganizationsFromMember.current
    );

    if (
      member &&
      member.organizations &&
      !hasSetOrganizationsFromMember.current
    ) {
      console.log(
        "🔍 DEBUG EditMember: Setting organizations from member data:",
        member.organizations
      );
      setSelectedOrganizations(member.organizations);
      hasSetOrganizationsFromMember.current = true;
    }
  }, [member]);

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First name is required").max(50),
    lastName: Yup.string().required("Last name is required").max(50),
    loginEmail: Yup.string()
      .email("Invalid email")
      .required("email is required"),
    phone: Yup.string().optional(),
    personalBusinessEmail: Yup.string().email("Invalid email").optional(),
    professionalTitle: Yup.string().max(100).optional(),
    membershipTier: Yup.mixed<"lite" | "premium">()
      .oneOf(["lite", "premium"])
      .required("Membership tier is required"),
    communityStatus: Yup.string()
      .oneOf(["unverified", "verified", "pending", "rejected"])
      .required("Community status is required"),
    verificationStatus: Yup.string()
      .oneOf(["pending", "verified", "rejected", "under_review"])
      .optional(),
    password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .matches(
        /^(?=.*[A-Z])/,
        "Password must contain at least one capital letter"
      )
      .matches(
        /^(?=.*[!@#$%^&*(),.?":{}|<>])/,
        "Password must contain at least one special symbol"
      )
      .matches(/^(?=.*[0-9])/, "Password must contain at least one number")
      .optional(),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref("password")], "Passwords must match")
      .when("password", {
        is: (val: string) => !!val,
        then: (schema) => schema.required("Please confirm your password"),
        otherwise: (schema) => schema.optional(),
      }),
  });

  const getInitialValues = (): UpdateMemberData => {
    console.log("🔍 DEBUG: getInitialValues called with member:", member);

    if (!member) {
      console.log("🔍 DEBUG: No member data, returning default values");
      return {
        firstName: "",
        lastName: "",
        loginEmail: "",
        phone: "",
        personalBusinessEmail: "",
        professionalTitle: "",
        membershipTier: "lite",
        communityStatus: "unverified",
        verificationStatus: undefined,
        selectedOrganizations: [],
      };
    }

    const initialValues = {
      firstName: member.firstName || "",
      lastName: member.lastName || "",
      loginEmail: member.loginEmail || "",
      phone: member.phone || "",
      personalBusinessEmail: member.personalBusinessEmail || "",
      professionalTitle: member.professionalTitle || "",
      membershipTier: member.membershipTier || "lite",
      communityStatus: member.communityStatus || "unverified",
      verificationStatus: member.verificationStatus || undefined,
      selectedOrganizations: member.organizations || [],
    };

    console.log("🔍 DEBUG: Returning initial values:", initialValues);
    return initialValues;
  };

  const formik = useFormik({
    initialValues: getInitialValues(),
    validationSchema,
    onSubmit: async (values) => {
      dispatch(clearUpdateMemberError());
      // Convert camelCase to snake_case for API submission
      const convertedValues = {
        ...values,
        organizations: selectedOrganizations,
        selectedOrganizations: selectedOrganizations,
      };

      dispatch(updateMemberRequest({ id: memberId, data: convertedValues }));
    },
    enableReinitialize: true,
  });

  const handleNext = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    router.push(`/members/${memberId}`);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (activeStep === steps.length - 1) {
      formik.handleSubmit(e);
    }
  };

  const handleOrganizationSelectionChange = (organizations: any[]) => {
    console.log(
      "🔍 DEBUG Form: Organization selection changed:",
      organizations
    );
    console.log(
      "🔍 DEBUG Form: Previous selectedOrganizations:",
      selectedOrganizations
    );
    setSelectedOrganizations(organizations);
    // Set both fields to ensure compatibility
    formik.setFieldValue("organizations", organizations);
    formik.setFieldValue("selectedOrganizations", organizations);
    console.log(
      "🔍 DEBUG Form: Updated selectedOrganizations state and formik values"
    );
  };

  const isStepValid = (step: number) => {
    console.log("🔍 DEBUG: isStepValid called for step:", step);
    console.log("🔍 DEBUG: Current formik values:", formik.values);
    console.log("🔍 DEBUG: Current formik errors:", formik.errors);

    switch (step) {
      case 0: // Basic Information
        const step0Valid =
          formik.values.firstName &&
          formik.values.lastName &&
          formik.values.loginEmail &&
          !formik.errors.firstName &&
          !formik.errors.lastName &&
          !formik.errors.loginEmail &&
          !formik.errors.phone &&
          !formik.errors.personalBusinessEmail &&
          !formik.errors.professionalTitle &&
          (!formik.values.password ||
            (!formik.errors.password &&
              !formik.errors.confirmPassword &&
              formik.values.confirmPassword));
        console.log("🔍 DEBUG: Step 0 valid:", step0Valid);
        return step0Valid;

      case 1: // Organization Details
        // Organization step is always valid since identityType is removed
        console.log("🔍 DEBUG: Step 1 valid (organization linking only)");
        return true;

      case 2: // Membership Settings
        const step2Valid =
          formik.values.membershipTier &&
          formik.values.communityStatus &&
          !formik.errors.membershipTier &&
          !formik.errors.communityStatus &&
          !formik.errors.verificationStatus;
        console.log("🔍 DEBUG: Step 2 valid:", step2Valid);
        return step2Valid;

      case 3: // Review
        console.log("🔍 DEBUG: Step 3 valid:", formik.isValid);
        return formik.isValid;

      default:
        return false;
    }
  };

  const canProceed = isStepValid(activeStep);

  // Debug logging for step validation
  useEffect(() => {
    console.log("🔍 DEBUG: Step validation update:", {
      activeStep,
      canProceed,
    });
  }, [
    activeStep,
    canProceed,
  ]);

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Update the member's basic personal and contact information.
            </Typography>
            <MemberForm
              formik={formik}
              showBasicFields={true}
              showOrganizationFields={false}
              showMembershipFields={false}
            />
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Organization
            </Typography>
            
            <MemberForm
              formik={formik}
              showBasicFields={false}
              showOrganizationLinking={true}
              showOrganizationFields={true}
              showMembershipFields={false}
              selectedOrganizations={selectedOrganizations}
              onOrganizationSelectionChange={handleOrganizationSelectionChange}
            />
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Membership Settings
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Update the member's membership tier and status.
            </Typography>
            <MemberForm
              formik={formik}
              showBasicFields={false}
              showOrganizationFields={false}
              showMembershipFields={true}
            />
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review & Update
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Review all changes before updating the member.
            </Typography>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Basic Information
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Name:</strong> {formik.values.firstName}{" "}
                  {formik.values.lastName}
                </Typography>
                <Typography variant="body2">
                  <strong>email:</strong> {formik.values.loginEmail}
                </Typography>
                {formik.values.phone && (
                  <Typography variant="body2">
                    <strong>Phone:</strong> {formik.values.phone}
                  </Typography>
                )}
                {formik.values.professionalTitle && (
                  <Typography variant="body2">
                    <strong>Title:</strong> {formik.values.professionalTitle}
                  </Typography>
                )}
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                Membership
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Membership Tier:</strong>{" "}
                  {formik.values.membershipTier}
                </Typography>
                <Typography variant="body2">
                  <strong>Community Status:</strong>{" "}
                  {formik.values.communityStatus}
                </Typography>
                {formik.values.verificationStatus && (
                  <Typography variant="body2">
                    <strong>Verification Status:</strong>{" "}
                    {formik.values.verificationStatus}
                  </Typography>
                )}
              </Box>

              {selectedOrganizations.length > 0 && (
                <>
                  <Typography variant="subtitle1" gutterBottom>
                    Associated Organizations
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    {selectedOrganizations.map((org, index) => (
                      <Typography key={org.uuid} variant="body2" sx={{ ml: 2 }}>
                        • {org.name}
                        {org.city && org.state
                          ? ` (${org.city}, ${org.state})`
                          : org.industry
                          ? ` (${org.industry})`
                          : ""}
                      </Typography>
                    ))}
                  </Box>
                </>
              )}

              {formik.values.organization?.name && (
                <>
                  <Typography variant="subtitle1" gutterBottom>
                    Organization
                  </Typography>
                  <Box>
                    <Typography variant="body2">
                      <strong>Name:</strong> {formik.values.organization.name}
                    </Typography>
                    {formik.values.organization.email && (
                      <Typography variant="body2">
                        <strong>email:</strong>{" "}
                        {formik.values.organization.email}
                      </Typography>
                    )}
                    {formik.values.organization.phone && (
                      <Typography variant="body2">
                        <strong>Phone:</strong>{" "}
                        {formik.values.organization.phone}
                      </Typography>
                    )}
                  </Box>
                </>
              )}
            </Paper>
          </Box>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="lg">
          <Box
            sx={{
              py: 3,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "400px",
            }}
          >
            <CircularProgress size={60} />
          </Box>
        </Container>
      </AppLayout>
    );
  }

  if (error || !member) {
    return (
      <AppLayout>
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            <Alert severity="error" sx={{ mb: 3 }}>
              {error || "Member not found"}
            </Alert>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => router.push("/members")}
            >
              Back to Members
            </Button>
          </Box>
        </Container>
      </AppLayout>
    );
  }

  return (
    <ErrorBoundary isPageLevel={true}>
      <AppLayout>
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Breadcrumbs */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link
                color="inherit"
                href="/members"
                onClick={(e) => {
                  e.preventDefault();
                  router.push("/members");
                }}
                sx={{ cursor: "pointer" }}
              >
                Members
              </Link>
              <Link
                color="inherit"
                href={`/members/${memberId}`}
                onClick={(e) => {
                  e.preventDefault();
                  router.push(`/members/${memberId}`);
                }}
                sx={{ cursor: "pointer" }}
              >
                {member.firstName} {member.lastName}
              </Link>
              <Typography color="text.primary">Edit</Typography>
            </Breadcrumbs>

            {/* Header */}
            <Box sx={{ mb: 4 }}>
              <Typography
                variant="h3"
                component="h1"
                gutterBottom
                sx={{
                  color: "#1e3a8a",
                  fontWeight: 700,
                  fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2.5rem" },
                }}
              >
                Edit Member
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{
                  fontSize: { xs: "0.875rem", md: "1rem" },
                }}
              >
                Update member information and settings
              </Typography>
            </Box>

            {/* Stepper */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Stepper activeStep={activeStep} alternativeLabel>
                  {steps.map((step, index) => (
                    <Step key={step.label}>
                      <StepLabel>
                        <Box>
                          <Typography variant="subtitle2">
                            {step.label}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {step.description}
                          </Typography>
                        </Box>
                      </StepLabel>
                    </Step>
                  ))}
                </Stepper>
              </CardContent>
            </Card>

            {/* Error Alert */}
            {(error || updateError) && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error || updateError}
              </Alert>
            )}

            {/* Form Content */}
            <Card>
              <CardContent sx={{ p: { xs: 2, md: 3 } }}>
                <form
                  onSubmit={handleSubmit}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && activeStep < steps.length - 1) {
                      e.preventDefault();
                    }
                  }}
                >
                  {renderStepContent(activeStep)}

                  {/* Action Buttons */}
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mt: 4,
                      gap: 2,
                    }}
                  >
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Button
                        type="button"
                        variant="outlined"
                        startIcon={<ArrowBackIcon />}
                        onClick={handleCancel}
                        disabled={updateLoading}
                      >
                        Cancel
                      </Button>
                      {activeStep > 0 && (
                        <Button
                          type="button"
                          variant="outlined"
                          onClick={handleBack}
                          disabled={updateLoading}
                        >
                          Back
                        </Button>
                      )}
                    </Box>

                    <Box sx={{ display: "flex", gap: 1 }}>
                      {activeStep < steps.length - 1 ? (
                        <Button
                          type="button"
                          variant="contained"
                          onClick={handleNext}
                          disabled={!canProceed || updateLoading}
                        >
                          Next
                        </Button>
                      ) : (
                        <Button
                          type="submit"
                          variant="contained"
                          startIcon={
                            updateLoading ? (
                              <CircularProgress size={20} />
                            ) : (
                              <SaveIcon />
                            )
                          }
                          disabled={!formik.isValid || updateLoading}
                        >
                          {updateLoading ? "Updating..." : "Update Member"}
                        </Button>
                      )}
                    </Box>
                  </Box>
                </form>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </AppLayout>
    </ErrorBoundary>
  );
}
