"use client";

import { PageLoading, useLoading } from "@/components/common";
import { ConfirmDialog } from "@/components/common/ConfirmDialog";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
import { ExportModal } from "@/components/members/ExportModal";
import { MemberFilters as MemberFiltersComponent } from "@/components/members/MemberFilters";
import { MemberTable } from "@/components/members/MemberTable";
import AppLayout from "@/layout/AppLayout";
import { membersService } from "@/services/members";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  bulkDeleteMembersRequest,
  exportMembersRequest,
  fetchMembersRequest,
  selectBulkDeleteLoading,
  selectMembersArray,
  selectMembersError,
  selectMembersLastFetched,
  selectMembersLoading,
  selectMembersPagination,
  selectMembersFilters,
  setCurrentPage,
  setPageSize,
} from "@/store/members/redux";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { Member, MemberSearchFilters } from "@/types/member";
import { showToast } from "@/utils/toast";
import { Add as AddIcon } from "@mui/icons-material";
import { Box, Button, Container, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function MembersPage() {
  const { setIsLoading, setLoadingMessage } = useLoading();

  const dispatch = useAppDispatch();
  const members = useAppSelector(selectMembersArray);
  const loading = useAppSelector(selectMembersLoading);
  const error = useAppSelector(selectMembersError);
  const lastFetched = useAppSelector(selectMembersLastFetched);
  const pagination = useAppSelector(selectMembersPagination);
  const bulkDeleteLoading = useAppSelector(selectBulkDeleteLoading);

  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const router = useRouter();

  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Members module for current user's role
  let memberPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const memberModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "members"
      );
      if (memberModule) {
        memberPermissions = memberModule;
      }
    }
  }

  // Table state - now managed by Redux
  const reduxFilters = useAppSelector(selectMembersFilters);
  const sortBy = reduxFilters.sortBy || "dateUpdated";
  const sortOrder = reduxFilters.sortOrder || "desc";

  // Filter state using new API structure
  const [filters, setFilters] = useState<MemberSearchFilters>({
    search: "",
    id: undefined,
    uuid: "",
    firstName: "",
    lastName: "",
    email: "",
    membershipTier: "",
    organizationName: "",
    organizationCity: "",
    organizationState: "",
    organizationZip: "",
    companySize: "",
    industry: "",
    dateCreatedFrom: "",
    dateCreatedTo: "",
    hasOrganizations: undefined,
    nameFilter: undefined,
  });

  useEffect(() => {
    dispatch(fetchMembersRequest({ filters }));
  }, [dispatch]);

  const handleFiltersChange = (newFilters: MemberSearchFilters) => {
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    dispatch(fetchMembersRequest({ filters, page: 1 }));
  };

  const handleClearFilters = () => {
    const clearedFilters: MemberSearchFilters = {
      search: "",
      id: undefined,
      uuid: "",
      firstName: "",
      lastName: "",
      email: "",
      membershipTier: "",
      organizationName: "",
      organizationCity: "",
      organizationState: "",
      organizationZip: "",
      companySize: "",
      industry: "",
      dateCreatedFrom: "",
      dateCreatedTo: "",
      hasOrganizations: undefined,
      nameFilter: undefined,
    };
    setFilters(clearedFilters);
    dispatch(fetchMembersRequest({ filters: clearedFilters, page: 1 }));
  };

  const handleSortChange = (
    newSortBy: string,
    newSortOrder: "asc" | "desc"
  ) => {
    dispatch(
      fetchMembersRequest({
        filters,
        sortBy: newSortBy,
        sortOrder: newSortOrder,
      })
    );
  };

  const handlePageChange = (newPage: number) => {
    dispatch(setCurrentPage(newPage));
    dispatch(
      fetchMembersRequest({
        filters: { ...filters, page: newPage },
        page: newPage,
      })
    );
  };

  const handlePageSizeChange = (newPageSize: number) => {
    dispatch(setPageSize(newPageSize));
    dispatch(
      fetchMembersRequest({
        filters: { ...filters, pageSize: newPageSize, page: 1 },
        pageSize: newPageSize,
        page: 1,
      })
    );
  };

  const handleSelectionChange = (newSelectedIds: string[]) => {
    setSelectedIds(newSelectedIds);
  };

  const handleEdit = (member: Member) => {
    router.push(`/members/edit/${member.uuid}`);
  };

  const handleDelete = (member: Member) => {
    setMemberToDelete(member);
    setShowDeleteDialog(true);
  };

  const handleView = (member: Member) => {
    router.push(`/members/${member.uuid}`);
  };

  const handleVerify = async (memberId: string) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Verifying member...");

      await membersService.verifyMember({
        memberId,
        verificationType: "manual",
        data: {},
      });

      dispatch(fetchMembersRequest());
      showToast.success("Member verified successfully");
    } catch (err: any) {
      console.error("Failed to verify member:", err);
      showToast.error("Failed to verify member. Please try again.");
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handlEmailVerify = async (memberId: string) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Verifying email...");

      await membersService.verifyemail(memberId);
      dispatch(fetchMembersRequest());
      showToast.success("Email verified successfully");
    } catch (err: any) {
      console.error("Failed to verify email:", err);
      showToast.error("Failed to verify email. Please try again.");
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleStatusChange = async (memberId: string, status: string) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Updating member status...");

      await membersService.updateMemberStatus(memberId, status);
      dispatch(fetchMembersRequest());
      showToast.success("Member status updated successfully");
    } catch (err: any) {
      console.error("Failed to update member status:", err);
      showToast.error("Failed to update member status. Please try again.");
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleBulkDelete = async () => {
    if (selectedIds.length === 0) return;
    setShowBulkDeleteDialog(true);
  };

  const handleBulkExport = async () => {
    if (selectedIds.length === 0) return;
    setShowExportModal(true);
  };

  const handleExport = () => {
    setShowExportModal(true);
  };

  const handleExportSubmit = (exportData: {
    filters: any;
    selectedFields: string[];
    notes: string;
  }) => {
    // Merge current page filters with export data and include sorting
    const exportPayload = {
      filters: { ...filters, ...exportData.filters },
      selectedFields: exportData.selectedFields,
      notes: exportData.notes,
      // Include current sorting parameters
      sortBy: sortBy,
      sortOrder: sortOrder,
    };

    dispatch(exportMembersRequest(exportPayload));
    setShowExportModal(false);
  };

  const handleBulkVerify = async () => {
    if (selectedIds.length === 0) return;

    try {
      setIsLoading(true);
      setLoadingMessage("Verifying selected members...");

      await membersService.bulkAction({
        memberIds: selectedIds,
        action: "verify",
      });

      setSelectedIds([]);
      dispatch(fetchMembersRequest());
      showToast.success(`${selectedIds.length} members verified successfully`);
    } catch (err: any) {
      console.error("Failed to verify selected members:", err);
      showToast.error("Failed to verify selected members. Please try again.");
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleAddMember = () => {
    router.push("/members/add");
  };

  const confirmDelete = async () => {
    if (!memberToDelete) return;

    try {
      setDeleteLoading(true);

      await membersService.deleteMember(memberToDelete.uuid);
      setShowDeleteDialog(false);
      setMemberToDelete(null);
      dispatch(fetchMembersRequest());
      showToast.success(
        `${memberToDelete.firstName} ${memberToDelete.lastName} deleted successfully`
      );
    } catch (err: any) {
      console.error("Failed to delete member:", err);
      showToast.error("Failed to delete member. Please try again.");
    } finally {
      setDeleteLoading(false);
    }
  };

  const confirmBulkDelete = async () => {
    if (selectedIds.length === 0) return;

    try {
      dispatch(bulkDeleteMembersRequest(selectedIds));
      setShowBulkDeleteDialog(false);
      setSelectedIds([]); // Clear selection after bulk delete
    } catch (err: any) {
      console.error("Failed to bulk delete members:", err);
      showToast.error("Failed to delete selected members. Please try again.");
    }
  };
  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    // Handle boolean values properly
    if (key === 'hasOrganizations') {
      return value !== undefined;
    }
    // Handle nameFilter - only count as active if not "all"
    if (key === 'nameFilter') {
      return value && value !== "all";
    }
    // Handle other values (strings, numbers, etc.)
    return value && value !== "";
  });

  // Show loading state
  if (loading && !members) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <PageLoading message="Loading members..." />
        </Container>
      </AppLayout>
    );
  }

  return (
    <ErrorBoundary isPageLevel={true}>
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            {/* Header */}
            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", md: "row" },
                alignItems: { xs: "flex-start", md: "center" },
                justifyContent: "space-between",
                mb: 4,
                gap: { xs: 2, md: 0 },
              }}
            >
              <Box>
                <Typography
                  variant="h3"
                  component="h1"
                  gutterBottom
                  sx={{
                    color: "#1e3a8a",
                    fontWeight: 700,
                    fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2.5rem" },
                  }}
                >
                  Members Management
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: "0.875rem", md: "1rem" },
                    display: { xs: "none", sm: "block" },
                  }}
                >
                  Manage and organize your chamber members with advanced
                  filtering and bulk operations
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  flexDirection: { xs: "column", sm: "row" },
                  width: { xs: "100%", sm: "auto" },
                }}
              >
                {memberPermissions.create && (
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleAddMember}
                    fullWidth={false}
                    sx={{ minWidth: { xs: "100%", sm: "auto" } }}
                  >
                    <Box sx={{ display: { xs: "none", sm: "block" } }}>
                      Add Member
                    </Box>
                    <Box sx={{ display: { xs: "block", sm: "none" } }}>Add</Box>
                  </Button>
                )}
              </Box>
            </Box>

            {/* Stats cards removed due to missing 'status' property on Member */}

            {/* Search and Filters */}
            <MemberFiltersComponent
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onApplyFilters={handleApplyFilters}
              onClearFilters={handleClearFilters}
              loading={loading}
            />

            {/* Members Table */}
            <MemberTable
              members={members as any}
              loading={loading}
              hasActiveFilters={hasActiveFilters}
              selectedIds={selectedIds}
              onSelectionChange={handleSelectionChange}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
              onVerify={handleVerify}
              onemailVerify={handlEmailVerify}
              onStatusChange={handleStatusChange}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              page={pagination.currentPage}
              pageSize={pagination.pageSize}
              total={pagination.totalCount}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              onBulkDelete={handleBulkDelete}
              onBulkExport={handleBulkExport}
              onBulkVerify={handleBulkVerify}
              onExport={handleExport}
              memberPermissions={memberPermissions}
            />

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
              open={showDeleteDialog}
              title="Delete Member"
              message={`Are you sure you want to delete ${memberToDelete?.firstName} ${memberToDelete?.lastName}? This action cannot be undone.`}
              onConfirm={confirmDelete}
              onClose={() => {
                setShowDeleteDialog(false);
                setMemberToDelete(null);
              }}
              loading={deleteLoading}
              severity="error"
              confirmText="Delete"
            />

            {/* Bulk Delete Confirmation Dialog */}
            <ConfirmDialog
              open={showBulkDeleteDialog}
              title="Bulk Delete Members"
              message={`Are you sure you want to delete ${selectedIds.length} selected member(s)? This action cannot be undone.`}
              onConfirm={confirmBulkDelete}
              onClose={() => setShowBulkDeleteDialog(false)}
              loading={bulkDeleteLoading}
              severity="error"
              confirmText="Delete All"
            />

            {/* Export Modal */}
            <ExportModal
              key={showExportModal ? "open" : "closed"}
              open={showExportModal}
              onClose={() => setShowExportModal(false)}
              onExport={handleExportSubmit}
              currentFilters={filters}
              loading={loading}
            />
          </Box>
        </Container>
      </AppLayout>
    </ErrorBoundary>
  );
}
