// View Organization Page
"use client";

import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import {
  Container,
  Box,
  Alert,
  CircularProgress,
  Button,
  Breadcrumbs,
  Link,
} from "@mui/material";
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";

// Components
import OrganizationDetails from "../../../components/organizations/OrganizationDetails";
import AppLayout from "../../../layout/AppLayout";
import { ConfirmDialog } from "../../../components/ui/ConfirmDialog";

// Redux imports
import { AppDispatch, RootState } from "../../../store";
import { organizationsActions } from "../../../store/organizations/redux";
import { organizationsSelectors } from "../../../store/organizations/selector";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";

// Types
import { NextPage } from "next";

const ViewOrganizationPage: NextPage = () => {
  const params = useParams();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const uuid = params.uuid as string;

  // Redux state
  const organization = useSelector(
    organizationsSelectors.selectCurrentOrganization
  );
  const loading = useSelector(organizationsSelectors.selectLoading);
  const error = useSelector(organizationsSelectors.selectError);

  // Local state
  const [activeTab, setActiveTab] = useState(0);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Load organization data on mount
  useEffect(() => {
    if (uuid) {
      dispatch(organizationsActions.fetchOrganization(uuid));
    }
  }, [uuid, dispatch]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle edit
  const handleEdit = () => {
    if (organization) {
      router.push(`/organizations/${organization.uuid}/edit`);
    }
  };

  // Handle delete
  const handleDelete = () => {
    if (organization) {
      setShowDeleteDialog(true);
    }
  };

  // Handle confirm delete
  const handleConfirmDelete = () => {
    if (organization) {
      dispatch(organizationsActions.deleteOrganization(organization.uuid));
      setShowDeleteDialog(false);
      router.push("/organizations");
    }
  };

  // Handle cancel delete
  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  // Breadcrumb navigation
  const breadcrumbs = [
    {
      label: "Home",
      icon: <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: "/dashboard",
    },
    {
      label: "Organizations",
      icon: <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: "/organizations",
    },
    {
      label: organization?.name || "Loading...",
      icon: <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: `/organizations/${uuid}`,
    },
  ];

  const handleBreadcrumbClick = (href: string) => {
    router.push(href);
  };

  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Organizations module for current user's role
  let orgPermissions = {
    view: false,
    create: false,
    edit: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const orgModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "organizations"
      );
      if (orgModule) {
        orgPermissions = {
          view: orgModule.view || false,
          create: orgModule.create || false,
          edit: orgModule.update || false, // Map 'update' to 'edit' for consistency
          delete: orgModule.delete || false,
        };
      }
    }
  }

  // Loading state
  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="lg" sx={{ py: 3 }}>
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            minHeight="400px"
          >
            <CircularProgress />
          </Box>
        </Container>
      </AppLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <AppLayout>
        <Container maxWidth="lg" sx={{ py: 3 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          <Button
            variant="outlined"
            onClick={() => router.push("/organizations")}
          >
            Back to Organizations
          </Button>
        </Container>
      </AppLayout>
    );
  }

  // Not found state
  if (!organization) {
    return (
      <AppLayout>
        <Container maxWidth="lg" sx={{ py: 3 }}>
          <Alert severity="warning" sx={{ mb: 3 }}>
            Organization not found
          </Alert>
          <Button
            variant="outlined"
            onClick={() => router.push("/organizations")}
          >
            Back to Organizations
          </Button>
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="lg" sx={{ py: 3 }}>
        {/* Breadcrumbs */}
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs aria-label="breadcrumb">
            {breadcrumbs.map((breadcrumb, index) => (
              <Link
                key={index}
                color={
                  index === breadcrumbs.length - 1 ? "text.primary" : "inherit"
                }
                href={breadcrumb.href}
                onClick={(e) => {
                  e.preventDefault();
                  handleBreadcrumbClick(breadcrumb.href);
                }}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  textDecoration: "none",
                  "&:hover": {
                    textDecoration:
                      index === breadcrumbs.length - 1 ? "none" : "underline",
                  },
                }}
              >
                {breadcrumb.icon}
                {breadcrumb.label}
              </Link>
            ))}
          </Breadcrumbs>
        </Box>

        {/* Action Buttons */}
        <Box
          sx={{ mb: 3, display: "flex", gap: 2, justifyContent: "flex-end" }}
        >
          {orgPermissions.edit && (
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={handleEdit}
            >
              Edit
            </Button>
          )}
          {orgPermissions.delete && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDelete}
            >
              Delete
            </Button>
          )}
        </Box>

        {/* Organization Details */}
        <OrganizationDetails
          organization={organization}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          onEdit={handleEdit}
          onDelete={handleDelete}
          orgPermissions={orgPermissions}
        />

        {/* Delete Confirmation Dialog */}
        <ConfirmDialog
          open={showDeleteDialog}
          title="Delete Organization"
          message={`Are you sure you want to delete "${organization?.name}"? This action cannot be undone.`}
          onConfirm={handleConfirmDelete}
          onCancel={handleCancelDelete}
          confirmText="Delete"
          cancelText="Cancel"
          severity="error"
        />
      </Container>
    </AppLayout>
  );
};

export default ViewOrganizationPage;
