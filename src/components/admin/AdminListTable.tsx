import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  Box,
  Typography,
  CircularProgress,
  TableSortLabel,
  Tooltip,
  IconButton,
} from "@mui/material";
import { Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { Admin } from "@/types/adminUser";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";

interface AdminListTableProps {
  users: Admin[];
  loading: boolean;
  page: number;
  pageSize: number;
  total: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onSortChange: (sortBy: string, sortOrder: "asc" | "desc") => void;
  onEdit?: (user: Admin) => void;
  onDelete?: (user: Admin) => void;
  currentUserRole?: string;
  adminPermissions: {
    view: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
  };
}

export function AdminListTable({
  users,
  loading,
  page,
  pageSize,
  total,
  sortBy,
  sortOrder,
  onPageChange,
  onPageSizeChange,
  onSortChange,
  onEdit,
  onDelete,
  currentUserRole = "admin",
  adminPermissions,
}: AdminListTableProps) {
  const handleSort = (field: string) => {
    const isAsc = sortBy === field && sortOrder === "asc";
    onSortChange(field, isAsc ? "desc" : "asc");
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "super_admin":
        return "error";
      case "admin":
        return "warning";
      case "moderator":
        return "info";
      default:
        return "default";
    }
  };

  const formatRoleName = (role: string) => {
    return role.replace("_", " ").toUpperCase();
  };

  if (loading) {
    return (
      <Paper sx={{ p: 4, textAlign: "center" }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading admin users...
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ width: "100%", overflow: "hidden" }}>
      <TableContainer>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>Username</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Roles</TableCell>
              {/* <TableCell>
                <TableSortLabel
                  active={sortBy === "last_login"}
                  direction={sortBy === "last_login" ? sortOrder : "asc"}
                  onClick={() => handleSort("last_login")}
                >
                  Last Login
                </TableSortLabel>
              </TableCell> */}
              {(onEdit || onDelete) && (
                <TableCell align="center" sx={{ width: 120 }}>
                  Actions
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {users.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={onEdit || onDelete ? 6 : 5}
                  align="center"
                  sx={{ py: 4 }}
                >
                  <Typography variant="body1" color="text.secondary">
                    No admin users found
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => {
                return (
                  <TableRow key={user.uuid} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {user.username}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{user.email}</Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", gap: 0.5, flexWrap: "wrap" }}>
                        {user.roles.length === 0 ? (
                          <Chip
                            label="No Roles"
                            size="small"
                            variant="outlined"
                            color="default"
                          />
                        ) : (
                          (Array.isArray(user.roles)
                            ? user.roles
                            : [user.roles]
                          ).map((role) => (
                            <Chip
                              key={role}
                              label={formatRoleName(role)}
                              size="small"
                              color={getRoleColor(role) as any}
                              variant="filled"
                            />
                          ))
                        )}
                      </Box>
                    </TableCell>
                    {/* <TableCell>
                      <Typography
                        variant="body2"
                        color={
                          user.last_login ? "text.primary" : "text.secondary"
                        }
                      >
                        {formatDate(user.last_login)}
                      </Typography>
                    </TableCell> */}

                    {(onEdit || onDelete) && (
                      <TableCell align="center">
                        <Box
                          sx={{
                            display: "flex",
                            gap: 0.5,
                            justifyContent: "center",
                          }}
                        >
                          {onEdit && (
                            <Tooltip
                              title={
                                adminPermissions.update
                                  ? "Edit User"
                                  : "Insufficient permissions"
                              }
                            >
                              <span>
                                <IconButton
                                  size="small"
                                  onClick={() => onEdit(user)}
                                  disabled={!adminPermissions.update}
                                  color="primary"
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </span>
                            </Tooltip>
                          )}
                          {onDelete && (
                            <Tooltip
                              title={
                                adminPermissions.delete
                                  ? "Delete User"
                                  : "Insufficient permissions"
                              }
                            >
                              <span>
                                <IconButton
                                  size="small"
                                  onClick={() => onDelete(user)}
                                  disabled={!adminPermissions.delete}
                                  color="error"
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </span>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        component="div"
        count={total}
        page={page - 1} // MUI uses 0-based indexing
        onPageChange={(_, newPage) => onPageChange(newPage + 1)}
        rowsPerPage={pageSize}
        onRowsPerPageChange={(event) =>
          onPageSizeChange(parseInt(event.target.value, 10))
        }
        rowsPerPageOptions={[5, 10, 25, 50]}
        showFirstButton
        showLastButton
      />
    </Paper>
  );
}
