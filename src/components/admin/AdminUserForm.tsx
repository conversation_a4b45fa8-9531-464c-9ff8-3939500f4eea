"use client";

import { CreateAdminUserRequest } from "@/services/adminAPI";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { Admin } from "@/types/adminUser";
import {
  createAdminUserRequest,
  updateAdminUserRequest,
} from "@/store/adminUser/redux";
import {
  selectCreateLoading,
  selectUpdateLoading,
  useCurrentUserRole,
} from "@/store/adminUser/selector";
import {
  Cancel as CancelIcon,
  Person as PersonIcon,
  Save as SaveIcon,
  Security as SecurityIcon,
} from "@mui/icons-material";
import {
  Alert,
  Backdrop,
  Box,
  Button,
  Chip,
  CircularProgress,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  TextField as MuiTextField,
  TextFieldProps as MuiTextFieldProps,
  OutlinedInput,
  Select,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import React, { useCallback, useMemo, useEffect } from "react";
import * as Yup from "yup";
import {
  getRoleColor,
  formatRoleDisplay,
  getPasswordStrength,
} from "./AdminUserForm/utils";
import FormField from "./AdminUserForm/FormField";
import {
  AdminRole,
  FormValues,
  AdminUserFormProps,
} from "./AdminUserForm/types";
import {
  RESERVED_USERNAMES,
  COMMON_PASSWORDS,
} from "./AdminUserForm/validation";

// Types
const phoneRegExp = /^(\+1)? ?\(?\d{3}\)?[-.● ]?\d{3}[-.● ]?\d{4}$/;
// Validation schemas
const createValidationSchema = (isEditMode: boolean) => {
  const passwordSchema = isEditMode
    ? Yup.string()
        .optional()
        .min(8, "Password must be at least 8 characters")
        .max(100, "Password must be less than 100 characters")
        .matches(
          /^(?=.*[a-z])/,
          "Password must contain at least one lowercase letter"
        )
        .matches(
          /^(?=.*[A-Z])/,
          "Password must contain at least one uppercase letter"
        )
        .matches(/^(?=.*\d)/, "Password must contain at least one number")
        .matches(
          /^(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/,
          "Password must contain at least one special character"
        )
        .test("no-spaces", "Password cannot contain spaces", (val) =>
          val ? !/\s/.test(val) : true
        )
        .test(
          "not-common",
          "Password is too common",
          (val) => !COMMON_PASSWORDS.includes(val?.toLowerCase() || "")
        )
    : Yup.string().optional()
       

  const confirmPasswordSchema = isEditMode
    ? Yup.string()
        .optional()
        .oneOf([Yup.ref("temporaryPassword")], "Passwords must match")
    : Yup.string()
        .required("Please confirm your password").optional()
        .oneOf([Yup.ref("temporaryPassword")], "Passwords must match");

  const baseSchema = {
    username: isEditMode
      ? Yup.string() // Optional for edit mode since it's not being updated
      : Yup.string()
          .min(3, "Username must be at least 3 characters")
          .max(50, "Username must be less than 50 characters")
          .matches(
            /^[a-zA-Z0-9_-]+$/,
            "Username can only contain letters, numbers, underscores, and hyphens"
          )
          .test(
            "no-consecutive-spaces",
            "Username cannot contain consecutive spaces",
            (val) => !val?.includes("  ")
          )
          .test(
            "not-reserved",
            "Username is reserved",
            (val) => !RESERVED_USERNAMES.includes(val?.toLowerCase() || "")
          )
          .required("Username is required"),

    email: Yup.string()
      .email("Please enter a valid email address")
      .max(100, "email must be less than 100 characters")
      .test("valid-format", "Please enter a valid email format", (val) => {
        if (!val) return true;
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailRegex.test(val);
      })
      .test(
        "no-consecutive-dots",
        "email cannot contain consecutive dots",
        (val) => !val?.includes("..")
      )
      .test("dot-position", "email cannot start or end with a dot", (val) =>
        val ? !val.startsWith(".") && !val.endsWith(".") : true
      )
      .required("email is required"),

    firstName: Yup.string()
      .max(50, "First name must be less than 50 characters")
      .required("First name is required"),

    lastName: Yup.string()
      .max(50, "Last name must be less than 50 characters")
      .required("Last name is required"),

    phone: Yup.string()
      .matches(phoneRegExp, "Phone number is not valid")
      .optional(),

    countrycode: Yup.string()
      .min(1, "Country code is required")
      .max(5, "Country code must be less than 5 characters")
      .required("Country code is required"),

    roles: Yup.array()
      .of(Yup.string())
      .min(1, "Please select a role")
      .required("Role is required"),

    temporaryPassword: passwordSchema,
    confirmPassword: confirmPasswordSchema,
  };

  return Yup.object({
    ...baseSchema,
    ...(isEditMode && { id: Yup.string().required("User ID is required") }),
  });
};

// Utility functions

// Custom hooks

// Reusable TextField Component

// Sub-components
const FormSection: React.FC<{
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}> = React.memo(({ title, icon, children }) => (
  <>
    <Typography
      variant="h6"
      gutterBottom
      sx={{ display: "flex", alignItems: "center", gap: 1 }}
    >
      {icon}
      {title}
    </Typography>
    {children}
  </>
));

const RoleChipRenderer = React.memo(({ selected }: { selected: string[] }) => (
  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
    {selected.map((role) => (
      <Chip
        key={role}
        label={formatRoleDisplay(role)}
        size="small"
        color={getRoleColor(role) as any}
      />
    ))}
  </Box>
));

const BasicInformationSection: React.FC<{
  formik: any;
  loading: boolean;
  isEditMode: boolean;
}> = React.memo(({ formik, loading, isEditMode }) => (
  <FormSection title="Basic Information" icon={<PersonIcon />}>
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={6}>
        <FormField
          name="username"
          formik={formik}
          fullWidth
          label="Username"
          disabled={loading || isEditMode} // Disable username field in edit mode
          maxLength={50}
          helpText={
            isEditMode
              ? "Username cannot be changed"
              : "Username must be unique and contain only letters, numbers, underscores, and hyphens"
          }
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormField
          name="email"
          formik={formik}
          fullWidth
          label="Email"
          type="email"
          disabled={loading}
          maxLength={100}
          helpText="Enter a valid business email address"
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormField
          name="firstName"
          formik={formik}
          fullWidth
          label="First Name"
          disabled={loading}
          maxLength={50}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormField
          name="lastName"
          formik={formik}
          fullWidth
          label="Last Name"
          disabled={loading}
          maxLength={50}
        />
      </Grid>
     
      {isEditMode && (
        <>
          <Grid item xs={12} sm={6}>
            <FormField
              name="temporaryPassword"
              formik={formik}
              fullWidth
              label="New Password (leave blank to keep current)"
              type="password"
              disabled={loading}
              maxLength={100}
              helpText="Leave blank to keep current password"
              showPasswordStrength={true}
              isEditMode={isEditMode}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormField
              name="confirmPassword"
              formik={formik}
              fullWidth
              label="Confirm New Password"
              type="password"
              disabled={loading}
              maxLength={100}
              helpText="Confirm your new password"
              isEditMode={isEditMode}
            />
          </Grid>
        </>
      )}
    </Grid>
  </FormSection>
));

const RoleStatusSection: React.FC<{
  formik: any;
  loading: boolean;
  currentUserRole: string;
}> = React.memo(({ formik, loading, currentUserRole }) => {
  const selectedRoles = formik.values.roles || [];

  const getRoleValidationMessage = useCallback(() => {
    if (selectedRoles.length === 0) {
      return "Please select a role";
    }
    return "";
  }, [selectedRoles]);

  const roleValidationMessage = getRoleValidationMessage();

  const { roles: rolesData } = useAppSelector((state) => state.roles);
  const currentRole = useCurrentUserRole();
  const roles = currentRole === "super_admin" ? rolesData : rolesData.filter((role) => role.slug !== "super_admin");


  return (
    <FormSection title="Role Assignment" icon={<SecurityIcon />}>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <FormControl
            fullWidth
            error={
              (formik.touched.roles && Boolean(formik.errors.roles)) ||
              Boolean(roleValidationMessage)
            }
          >
            <InputLabel>Roles</InputLabel>
            <Select
              name="roles"
              value={formik.values.roles || ""}
              onChange={(e) => {
                console.log(e.target.value, "e.target.value");
                formik.setFieldValue("roles", [e.target.value]);
              }}
              onBlur={formik.handleBlur}
              input={<OutlinedInput label="Roles" />}
              disabled={loading}
            >
              {roles.map(({ name, description, slug }) => (
                <MenuItem key={slug} value={slug}>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                    }}
                  >
                    <Typography>{formatRoleDisplay(name)}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
            {((formik.touched.roles && formik.errors.roles) ||
              roleValidationMessage) && (
              <FormHelperText>
                {(formik.touched.roles && formik.errors.roles) ||
                  roleValidationMessage}
              </FormHelperText>
            )}
            <FormHelperText>
              Select appropriate roles based on user responsibilities. Roles
              determine access levels and permissions.
            </FormHelperText>
          </FormControl>
        </Grid>
      </Grid>
    </FormSection>
  );
});

const ContactInformationSection: React.FC<{
  formik: any;
  loading: boolean;
}> = React.memo(({ formik, loading }) => (
  <FormSection title="Contact Information" icon={<PersonIcon />}>
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={8}>
        <FormField
          name="phone"
          formik={formik}
          fullWidth
          label="Phone Number"
          disabled={loading}
          maxLength={20}
        />
      </Grid>
    </Grid>
  </FormSection>
));

const FormActions: React.FC<{
  onCancel: () => void;
  onSubmit: () => void;
  loading: boolean;
  isEditMode: boolean;
  isValid: boolean;
  isDirty: boolean;
}> = React.memo(
  ({ onCancel, onSubmit, loading, isEditMode, isValid, isDirty }) => (
    <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", mt: 4 }}>
      <Button
        variant="outlined"
        onClick={onCancel}
        disabled={loading}
        startIcon={<CancelIcon />}
      >
        Cancel
      </Button>
      <Button
        onClick={onSubmit}
        variant="contained"
        disabled={loading || !isValid || (!isDirty && isEditMode)}
        startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
        sx={{
          background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
          color: "#fff",
          "&:hover": {
            opacity: 0.95,
          },
          "&:disabled": {
            background: "#ccc",
            color: "#666",
          },
        }}
      >
        {loading ? "Processing..." : isEditMode ? "Update User" : "Create User"}
      </Button>
    </Box>
  )
);

// Main component
export function AdminUserForm({
  adminUser,
  currentUserRole = "admin",
  onSubmit,
  onCancel,
  loading = false,
  error,
}: AdminUserFormProps) {
  const dispatch = useAppDispatch();
  const isEditMode = Boolean(adminUser);
  const validRoles = useAppSelector((state) =>
    state.roles.roles.map((r) => r.slug)
  );
  const createLoading = useAppSelector(selectCreateLoading);
  const updateLoading = useAppSelector(selectUpdateLoading);
  const isFormLoading = loading || createLoading || updateLoading;

  const initialValues: FormValues = useMemo(() => {
    if (isEditMode && adminUser) {
      // Ensure roles is an array of AdminRole
      let rolesArr: AdminRole[] = [];
      if (Array.isArray(adminUser.roles)) {
        rolesArr = adminUser.roles
          .filter((r: any) => validRoles.includes(r))
          .map((r: any) => r as AdminRole);
      } else if (typeof adminUser.roles === "string") {
        // Handle comma-separated string or single role string
        const rolesSplit = adminUser.roles.includes(",")
          ? adminUser.roles.split(",")
          : [adminUser.roles];
        rolesArr = rolesSplit
          .map((r) => r.trim())
          .filter((r: any) => validRoles.includes(r))
          .map((r: any) => r as AdminRole);
      }
      return {
        id: adminUser.uuid,
        username: adminUser.username,
        email: adminUser.email,
        firstName: adminUser.firstName || "",
        lastName: adminUser.lastName || "",
        phone: adminUser.phone || "",
        countrycode: adminUser.countrycode || "+1", // Use the countrycode from API
        roles: rolesArr,
        temporaryPassword: "",
        confirmPassword: "",
      };
    }
    return {
      username: "",
      email: "",
      firstName: "",
      lastName: "",
      phone: "",
      countrycode: "+1",
      roles: [] as AdminRole[],
      temporaryPassword: "",
      confirmPassword: "",
    };
  }, [adminUser, isEditMode]);

  const validationSchema = useMemo(
    () => createValidationSchema(isEditMode),
    [isEditMode]
  );

  const handleCreateUser = useCallback(
    async (data: CreateAdminUserRequest) => {
      try {
        // Ensure roles is defined
        const createData = {
          ...data,
          roles:
            data.roles ||
            (["admin"] as ("admin" | "super_admin" | "moderator")[]),
        };

        // Call onSubmit callback - let parent handle the API call
        await onSubmit?.(createData);
      } catch (err: any) {
        console.error("Create user error:", err);
        throw err;
      }
    },
    [onSubmit]
  );

  const handleFormSubmit = useCallback(
    async (values: FormValues) => {
      try {
        const roles = values.roles || [];

        // Validate current user can assign selected roles
        const canAssignAllRoles = roles.every(
          (role: string) => true // Removed canManageRole check
        );
        if (!canAssignAllRoles) {
          console.error(
            "User does not have permission to assign selected roles"
          );
          return;
        }

        // Validate password strength for new users
        if (!isEditMode && values.temporaryPassword) {
          const passwordStrength = getPasswordStrength(
            values.temporaryPassword
          );
          if (passwordStrength.score < 3) {
            console.error("Password is too weak");
            return;
          }
        }

        if (isEditMode && adminUser) {
          // Only pass the allowed fields for update API (excluding username)
          const updateData = {
            ...(values.temporaryPassword && {
              password: values.temporaryPassword,
            }),
            email: values.email.toLowerCase().trim(),
            firstName: values.firstName,
            lastName: values.lastName,
            phone: values.phone,
            countrycode: values.countrycode,
            isactive: true, // Set based on your business logic
            istemppassword: Boolean(values.temporaryPassword), // Set to true if password is provided
            roles: values.roles,
          };

          // Call onSubmit callback - let parent handle the API call
          await onSubmit(updateData);
        } else {
          const createRequest = {
            email: values.email.toLowerCase().trim(),
            username: values.username.trim(),
            password: values.temporaryPassword,
            roles: values.roles,
            phone: values.phone,
            firstName: values.firstName,
            lastName: values.lastName,
          };
          await handleCreateUser(createRequest);
        }
      } catch (err: any) {
        console.error("Form submission error:", err);
      }
    },
    [currentUserRole, isEditMode, onSubmit, handleCreateUser]
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: handleFormSubmit,
    enableReinitialize: true,
  });
  console.log(formik.isValid, "formik.isValid");

  // Show feedback when user data is loaded and form is populated - removed to avoid redundant toasts

  console.log(formik, "1111111111");

  // Show loading state when fetching user data for edit mode
  const isLoadingUserData = !adminUser && loading;
  console.log(
    isEditMode,
    !adminUser?.username,
    loading,
    "loadingloadingloadingloading"
  );
  return (
    <Box component="form" sx={{ width: "100%", position: "relative" }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading overlay when fetching user data */}
      {isLoadingUserData && (
        <Backdrop
          sx={{
            position: "absolute",
            color: "primary.main",
            zIndex: 1,
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            borderRadius: 1,
          }}
          open={true}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 2,
            }}
          >
            <CircularProgress />
            <Typography variant="body2" color="text.secondary">
              Loading user data...
            </Typography>
          </Box>
        </Backdrop>
      )}

      <BasicInformationSection
        formik={formik}
        loading={isFormLoading}
        isEditMode={isEditMode}
      />

      <ContactInformationSection formik={formik} loading={isFormLoading} />

      <RoleStatusSection
        formik={formik}
        loading={isFormLoading}
        currentUserRole={currentUserRole}
      />

      <FormActions
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        loading={isFormLoading}
        isEditMode={isEditMode}
        isValid={formik.isValid}
        isDirty={formik.dirty}
      />
    </Box>
  );
}
