"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useBackgroundTokenRefresh } from "@/hooks/useBackgroundTokenRefresh";
import { useUserInactivityLogout } from "@/hooks/useUserInactivityLogout";
import { useSiteRevisitTimeout } from "@/hooks/useSiteRevisitTimeout";
import { InactivityWarningDialog } from "./InactivityWarningDialog";
import { performCompleteLogout, setCheckingTimeoutFlag } from "@/utils/auth";
import { useRouter } from "next/navigation";

interface SessionManagerProps {
  // Token refresh settings
  checkInterval?: number; // Check every X milliseconds (default: 30 seconds)
  refreshBuffer?: number; // Refresh X minutes before expiry (default: 5 minutes)

  // Retry settings for token refresh
  maxRetries?: number; // Maximum retry attempts for token refresh (default: 3)
  retryDelay?: number; // Base retry delay in milliseconds (default: 1000)
  maxRetryDelay?: number; // Maximum retry delay in milliseconds (default: 10000)
  enableNetworkAwareness?: boolean; // Enable network status tracking (default: true)

  // Inactivity settings
  inactivityTimeout?: number; // Auto logout after X minutes of inactivity (default: 30 minutes)
  enableInactivityLogout?: boolean; // Enable/disable inactivity logout (default: true)
  warningTime?: number; // Show warning X minutes before logout (default: 5)
  showWarningDialog?: boolean; // Show warning dialog (default: true)

  // Warning dialog settings
  warningAutoCloseTime?: number; // Auto close warning dialog after X seconds (default: 60)

  // Site revisit timeout settings
  siteRevisitTimeoutHours?: number; // Logout if user revisits after X hours (default: 1)
  enableSiteRevisitTimeout?: boolean; // Enable/disable site revisit timeout (default: true)
  checkSiteRevisitOnMount?: boolean; // Check timeout when component mounts (default: true)
  checkSiteRevisitOnFocus?: boolean; // Check timeout when window gains focus (default: true)
  checkSiteRevisitOnVisibilityChange?: boolean; // Check timeout when tab becomes visible (default: true)
}

/**
 * Comprehensive session management component that handles:
 * - Background token refresh with retry logic and network awareness
 * - User inactivity tracking and automatic logout
 * - Warning dialog for impending logout
 * - Site revisit timeout (logout if user returns after being away for more than 1 hour)
 *
 * Add this to your app layout to enable complete session management
 */
export const SessionManager: React.FC<SessionManagerProps> = ({
  // Token refresh defaults
  checkInterval = 30000, // 30 seconds
  refreshBuffer = 5, // 5 minutes

  // Retry defaults
  maxRetries = 3,
  retryDelay = 1000, // 1 second
  maxRetryDelay = 10000, // 10 seconds
  enableNetworkAwareness = true,

  // Inactivity defaults
  inactivityTimeout = 30, // 30 minutes
  enableInactivityLogout = true,
  warningTime = 5, // 5 minutes
  showWarningDialog = true,

  // Warning dialog defaults
  warningAutoCloseTime = 60, // 60 seconds

  // Site revisit timeout defaults
  siteRevisitTimeoutHours = 1, // 1 hour
  enableSiteRevisitTimeout = true,
  checkSiteRevisitOnMount = true,
  checkSiteRevisitOnFocus = true,
  checkSiteRevisitOnVisibilityChange = true,
}) => {
  const router = useRouter();
  const [showWarning, setShowWarning] = useState(false);
  const [warningTimeLeft, setWarningTimeLeft] = useState(0);

  // Set up background token refresh with enhanced retry logic
  const backgroundRefreshControls = useBackgroundTokenRefresh({
    checkInterval,
    refreshBuffer,
    maxRetries,
    retryDelay,
    maxRetryDelay,
    enableNetworkAwareness,
  });

  // Handle warning display
  const handleInactivityWarning = useCallback(
    (timeLeft: number) => {
      if (showWarningDialog) {
        setWarningTimeLeft(timeLeft);
        setShowWarning(true);
      }
    },
    [showWarningDialog]
  );

  // Handle manual logout
  const handleLogoutNow = useCallback(async () => {
    setShowWarning(false);
    try {
      performCompleteLogout();
      router.push("/");
    } catch (error) {
      console.error("Error during manual logout:", error);
      window.location.href = "/";
    }
  }, [router]);

  // Set up user inactivity logout with warning
  const inactivityLogoutControls = useUserInactivityLogout({
    timeout: inactivityTimeout,
    enabled: enableInactivityLogout,
    warningTime,
    onWarning: handleInactivityWarning,
    checkInterval: 10000, // Check every 10 seconds
  });

  // Set up site revisit timeout
  const siteRevisitTimeoutControls = useSiteRevisitTimeout({
    timeoutHours: siteRevisitTimeoutHours,
    enabled: enableSiteRevisitTimeout,
    checkOnMount: checkSiteRevisitOnMount,
    checkOnFocus: checkSiteRevisitOnFocus,
    checkOnVisibilityChange: checkSiteRevisitOnVisibilityChange,
  });

  // CRITICAL FIX: Initialize activity timestamp immediately when SessionManager mounts
  useEffect(() => {
    // Set flag to prevent last activity updates during initialization
    setCheckingTimeoutFlag(true);
    
    // Initialize the last activity timestamp for new sessions
    import("@/utils/auth").then(({ updateLastActivity }) => {
      updateLastActivity();
      // Allow last activity updates after initialization
      setCheckingTimeoutFlag(false);
    });
  }, []);

  // Debug logging for session management
  // console.log("🔧 SessionManager initialized with configuration:", {
  //   // Token refresh settings
  //   checkInterval: `${checkInterval}ms`,
  //   refreshBuffer: `${refreshBuffer} minutes`,
  //   maxRetries,
  //   retryDelay: `${retryDelay}ms`,
  //   maxRetryDelay: `${maxRetryDelay}ms`,
  //   enableNetworkAwareness,

  //   // Inactivity settings
  //   inactivityTimeout: `${inactivityTimeout} minutes`,
  //   enableInactivityLogout,
  //   warningTime: `${warningTime} minutes`,
  //   showWarningDialog,
  //   warningAutoCloseTime: `${warningAutoCloseTime} seconds`,

  //   // Site revisit settings
  //   siteRevisitTimeoutHours: `${siteRevisitTimeoutHours} hour(s)`,
  //   enableSiteRevisitTimeout,
  //   checkSiteRevisitOnMount,
  //   checkSiteRevisitOnFocus,
  //   checkSiteRevisitOnVisibilityChange,
  // });

  // Handle stay logged in
  const handleStayLoggedIn = useCallback(() => {
    setShowWarning(false);
    // The inactivity hook will automatically reset the timer when user activity is detected
    // We can also manually trigger activity reset
    if (inactivityLogoutControls.resetActivity) {
      inactivityLogoutControls.resetActivity();
    }
    // Update site revisit activity as well
    if (siteRevisitTimeoutControls.updateActivity) {
      siteRevisitTimeoutControls.updateActivity();
    }
    // Trigger immediate token refresh to ensure tokens are fresh
    if (backgroundRefreshControls.performSilentRefresh) {
      backgroundRefreshControls.performSilentRefresh();
    }
  }, [
    inactivityLogoutControls,
    siteRevisitTimeoutControls,
    backgroundRefreshControls,
  ]);

  // Handle manual token refresh
  const handleManualTokenRefresh = useCallback(async () => {
    console.log("🔄 Manual token refresh requested");
    if (backgroundRefreshControls.performSilentRefresh) {
      const success = await backgroundRefreshControls.performSilentRefresh();
      if (success) {
        console.log("✅ Manual token refresh successful");
      } else {
        console.error("❌ Manual token refresh failed");
      }
    }
  }, [backgroundRefreshControls]);

  // Log network status changes
  React.useEffect(() => {
    if (backgroundRefreshControls.networkStatus) {
      const { isOnline, consecutiveFailures, lastNetworkError } =
        backgroundRefreshControls.networkStatus;
      // console.log("🌐 Network status update:", {
      //   isOnline,
      //   consecutiveFailures,
      //   lastNetworkError: lastNetworkError?.toISOString(),
      // });
    }
  }, [backgroundRefreshControls.networkStatus]);

  return (
    <>
      {/* Inactivity warning dialog */}
      {showWarningDialog && (
        <InactivityWarningDialog
          open={showWarning}
          timeLeft={warningTimeLeft}
          onStayLoggedIn={handleStayLoggedIn}
          onLogoutNow={handleLogoutNow}
          autoCloseTime={warningAutoCloseTime}
        />
      )}
    </>
  );
};
