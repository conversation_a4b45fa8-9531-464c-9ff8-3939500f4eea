import React from "react";
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Grid,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
} from "@mui/material";
import {
  Security as SecurityIcon,
  Apple as AppleIcon,
  Google as GoogleIcon,
  LinkedIn as LinkedInIcon,
  Person as PersonIcon,
  MoreHoriz as MoreIcon,
} from "@mui/icons-material";
import { AuthenticationMetrics } from "@/types/member";

interface AuthenticationMetricsProps {
  metrics: AuthenticationMetrics | null;
  loading: boolean;
  error: string | null;
}

const AuthenticationMetricsCard: React.FC<AuthenticationMetricsProps> = ({
  metrics,
  loading,
  error,
}) => {
  if (loading) {
    return (
      <Card sx={{ height: "100%" }}>
        <CardContent>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: 200,
            }}
          >
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ height: "100%" }}>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) {
    return (
      <Card sx={{ height: "100%" }}>
        <CardContent>
          <Typography variant="body2" color="text.secondary">
            No authentication metrics available
          </Typography>
        </CardContent>
      </Card>
    );
  }

  const authMethods = [
    {
      name: "Username/Password",
      count: metrics.usernamePasswordCount,
      icon: <PersonIcon />,
      color: "#1976d2",
      percentage: ((metrics.usernamePasswordCount / metrics.totalMembers) * 100).toFixed(1),
    },
    {
      name: "Google OAuth2",
      count: metrics.googleOAuth2Count,
      icon: <GoogleIcon />,
      color: "#db4437",
      percentage: ((metrics.googleOAuth2Count / metrics.totalMembers) * 100).toFixed(1),
    },
    {
      name: "Apple",
      count: metrics.appleCount,
      icon: <AppleIcon />,
      color: "#000000",
      percentage: ((metrics.appleCount / metrics.totalMembers) * 100).toFixed(1),
    },
    {
      name: "LinkedIn",
      count: metrics.linkedinCount,
      icon: <LinkedInIcon />,
      color: "#0077b5",
      percentage: ((metrics.linkedinCount / metrics.totalMembers) * 100).toFixed(1),
    },
    {
      name: "Other",
      count: metrics.otherCount,
      icon: <MoreIcon />,
      color: "#666666",
      percentage: ((metrics.otherCount / metrics.totalMembers) * 100).toFixed(1),
    },
  ];

  return (
    <Card sx={{ height: "100%" }}>
      <CardContent>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <SecurityIcon sx={{ mr: 1, color: "primary.main" }} />
          <Typography variant="h6" component="h3">
            Authentication Methods
          </Typography>
        </Box>

        <Typography variant="h4" component="div" sx={{ mb: 2, fontWeight: "bold" }}>
          {metrics.totalMembers.toLocaleString()}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Total Members
        </Typography>

        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={2}>
          {authMethods.map((method) => (
            <Grid item xs={12} sm={6} key={method.name}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  p: 1.5,
                  borderRadius: 1,
                  backgroundColor: "background.paper",
                  border: `1px solid ${method.color}20`,
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    width: 40,
                    height: 40,
                    borderRadius: "50%",
                    backgroundColor: `${method.color}15`,
                    color: method.color,
                    mr: 2,
                  }}
                >
                  {method.icon}
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    {method.name}
                  </Typography>
                  <Typography variant="h6" component="div" sx={{ fontWeight: "bold" }}>
                    {method.count.toLocaleString()}
                  </Typography>
                  <Chip
                    label={`${method.percentage}%`}
                    size="small"
                    sx={{
                      backgroundColor: `${method.color}15`,
                      color: method.color,
                      fontSize: "0.75rem",
                      height: 20,
                    }}
                  />
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default AuthenticationMetricsCard; 