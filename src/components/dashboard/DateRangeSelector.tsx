import {
  CalendarMonth as CalendarIcon,
  CompareArrows as CompareIcon,
  Edit as EditIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Chip,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Tooltip,
  Typography
} from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import React, { useState, useMemo } from "react";

export interface DateRange {
  startDate: string | null;
  endDate: string | null;
  label: string;
}

export interface DateRangeSelectorProps {
  selectedRange: DateRange;
  onRangeChange: (range: DateRange) => void;
  disabled?: boolean;
  // New props for comparison functionality
  showComparison?: boolean;
  onComparisonToggle?: (show: boolean) => void;
  comparisonRange?: DateRange;
  onComparisonRangeChange?: (range: DateRange) => void;
}


export const getDateRangeUtils = () => {
  const now = new Date();

  // Get start and end of current month in UTC
  const getCurrentMonthRange = () => {
    // First day of current month in UTC at 00:00:00.000
    const startDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1, 0, 0, 0, 0));
    
    // Last day of current month in UTC at 23:59:59.999
    const endDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() + 1, 0, 23, 59, 59, 999));
    
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  };

  // Get start and end of previous month in UTC
  const getLastMonthRange = () => {
    // First day of previous month in UTC at 00:00:00.000
    const startDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() - 1, 1, 0, 0, 0, 0));
    
    // Last day of previous month in UTC at 23:59:59.999
    const endDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 0, 23, 59, 59, 999));
    
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  };

  // Get last 7 days including today in UTC
  const getLast7DaysRange = () => {
    // 6 days ago in UTC at 00:00:00.000
    const utcToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
    const startDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() - 6, 0, 0, 0, 0));
    
    // Today in UTC at 23:59:59.999
    const endDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999));
    
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  };

  // Get last 30 days including today in UTC
  const getLast30DaysRange = () => {
    // 29 days ago in UTC at 00:00:00.000
    const startDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() - 29, 0, 0, 0, 0));
    
    // Today in UTC at 23:59:59.999
    const endDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999));
    
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  };

  // Get current year range in UTC
  const getCurrentYearRange = () => {
    // January 1st of current year in UTC at 00:00:00.000
    const startDate = new Date(Date.UTC(now.getUTCFullYear(), 0, 1, 0, 0, 0, 0));
    
    // December 31st of current year in UTC at 23:59:59.999
    const endDate = new Date(Date.UTC(now.getUTCFullYear(), 11, 31, 23, 59, 59, 999));
    
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  };

  return {
    getCurrentMonthRange,
    getLastMonthRange,
    getLast7DaysRange,
    getLast30DaysRange,
    getCurrentYearRange,
  };
};

export const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  selectedRange,
  onRangeChange,
  disabled = false,
  showComparison = false,
  onComparisonToggle,
  comparisonRange,
  onComparisonRangeChange,
}) => {
  const [isCustomDialogOpen, setIsCustomDialogOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState<Date | null>(null);
  const [customEndDate, setCustomEndDate] = useState<Date | null>(null);
  const [isComparisonCustomDialogOpen, setIsComparisonCustomDialogOpen] = useState(false);
  const [comparisonCustomStartDate, setComparisonCustomStartDate] = useState<Date | null>(null);
  const [comparisonCustomEndDate, setComparisonCustomEndDate] = useState<Date | null>(null);

  // Dynamic preset options that calculate dates based on current date
  const presetOptions = useMemo(() => {
    const dateUtils = getDateRangeUtils();
    
    return [
      {
        label: "This Month",
        ...dateUtils.getCurrentMonthRange(),
      },
      {
        label: "Last Month",
        ...dateUtils.getLastMonthRange(),
      },
      {
        label: "Last 7 Days",
        ...dateUtils.getLast7DaysRange(),
      },
      {
        label: "All Time",
        startDate: null,
        endDate: null,
      },
    ];
  }, []); // Empty dependency array since we want this to be calculated once per render

  const handlePresetChange = (event: SelectChangeEvent<string>) => {
    const selectedLabel = event.target.value;

    if (selectedLabel === "Custom Range") {
      // If custom range is already selected, open dialog with current values
      if (selectedRange.label === "Custom Range") {
        const startDate = new Date(selectedRange.startDate || "");
        const endDate = new Date(selectedRange.endDate || "");
        setCustomStartDate(startDate);
        setCustomEndDate(endDate);
      } else {
        // Clear custom dates for new selection
        setCustomStartDate(null);
        setCustomEndDate(null);
      }
      setIsCustomDialogOpen(true);
      return;
    }

    const range = presetOptions.find((r) => r.label === selectedLabel);
    if (range) {

      onRangeChange(range);
    }
  };

  const handleComparisonPresetChange = (event: SelectChangeEvent<string>) => {
    const selectedLabel = event.target.value;

    if (selectedLabel === "Custom Range") {
      // If custom range is already selected, open dialog with current values
      if (comparisonRange?.label === "Custom Range") {
        const startDate = new Date(comparisonRange.startDate || "");
        const endDate = new Date(comparisonRange.endDate || "");
        setComparisonCustomStartDate(startDate);
        setComparisonCustomEndDate(endDate);
      } else {
        // Clear custom dates for new selection
        setComparisonCustomStartDate(null);
        setComparisonCustomEndDate(null);
      }
      setIsComparisonCustomDialogOpen(true);
      return;
    }

    const range = presetOptions.find((r) => r.label === selectedLabel);
    if (range && onComparisonRangeChange) {
      console.log("🔄 Comparison date range changed to:", range.label);
      onComparisonRangeChange(range);
    }
  };

  const handleCustomDateConfirm = () => {
    if (customStartDate && customEndDate) {
      const customRange: DateRange = {
        label: "Custom Range",
        startDate: customStartDate.toISOString(),
        endDate: customEndDate.toISOString(),
      };
      console.log("🔄 Custom date range selected:", customRange);
      onRangeChange(customRange);
      setIsCustomDialogOpen(false);
    }
  };

  const handleComparisonCustomDateConfirm = () => {
    if (comparisonCustomStartDate && comparisonCustomEndDate && onComparisonRangeChange) {
      const customRange: DateRange = {
        label: "Custom Range",
        startDate: comparisonCustomStartDate.toISOString(),
        endDate: comparisonCustomEndDate.toISOString(),
      };

      onComparisonRangeChange(customRange);
      setIsComparisonCustomDialogOpen(false);
    }
  };

  const handleCustomDateCancel = () => {
    setIsCustomDialogOpen(false);
    setCustomStartDate(null);
    setCustomEndDate(null);
  };

  const handleComparisonCustomDateCancel = () => {
    setIsComparisonCustomDialogOpen(false);
    setComparisonCustomStartDate(null);
    setComparisonCustomEndDate(null);
  };

  const formatCustomRangeLabel = () => {
    if (selectedRange.label === "Custom Range") {
      const start = new Date(selectedRange.startDate || "");
      const end = new Date(selectedRange.endDate || "");
      return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
    }
    return selectedRange.label;
  };

  const formatComparisonCustomRangeLabel = () => {
    if (comparisonRange?.label === "Custom Range") {
      const start = new Date(comparisonRange.startDate || "");
      const end = new Date(comparisonRange.endDate || "");
      return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
    }
    return comparisonRange?.label || "Select Range";
  };

  const handleComparisonToggle = () => {
    if (onComparisonToggle) {
      onComparisonToggle(!showComparison);
    }
  };


  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}  >
      <Box sx={{ minWidth: 250, display: 'flex',gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
        {/* Primary Date Range */}
        <Box sx={{ 
          mb: 3,
          p: 2,
          border: '1px solid',
          borderColor: 'primary.light',
          borderRadius: 2,
          backgroundColor: 'primary.50',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: -1,
            left: -1,
            right: -1,
            bottom: -1,
            borderRadius: 2,
            background: 'linear-gradient(45deg, primary.light, primary.main)',
            zIndex: -1,
            opacity: 0.2,
          }
        }}>
          <Typography variant="h6" gutterBottom sx={{ 
            color: 'primary.main', 
            fontWeight: 700,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            mb: 2
          }}>
            <CalendarIcon fontSize="small" />
            Primary Date Range
          </Typography>
          <FormControl fullWidth size="small" disabled={disabled}>
            <InputLabel id="date-range-select-label">Date Range</InputLabel>
            <Select
              labelId="date-range-select-label"
              id="date-range-select"
              value={selectedRange.label}
              label="Date Range"
              onChange={handlePresetChange}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: 'primary.main',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                  },
                },
              }}
            >
              {presetOptions.map((option) => (
                <MenuItem key={option.label} value={option.label}>
                  {option.label}
                </MenuItem>
              ))}
              <MenuItem value="Custom Range">
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <CalendarIcon fontSize="small" />
                  Custom Range
                </Box>
              </MenuItem>
            </Select>
          </FormControl>

          {/* Display custom range label if selected */}
          {selectedRange.label === "Custom Range" && (
            <Box sx={{ mt: 2 }}>
              <Tooltip title="Click to edit custom date range">
                <Chip
                  label={
                    <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                      {formatCustomRangeLabel()}
                      <EditIcon fontSize="small" />
                    </Box>
                  }
                  size="small"
                  color="primary"
                  variant="outlined"
                  onClick={() => {
                    // Allow clicking the chip to edit the custom range
                    const startDate = new Date(selectedRange.startDate || "");
                    const endDate = new Date(selectedRange.endDate || "");
                    setCustomStartDate(startDate);
                    setCustomEndDate(endDate);
                    setIsCustomDialogOpen(true);
                  }}
                  sx={{
                    cursor: "pointer",
                    "&:hover": { 
                      backgroundColor: "primary.light",
                      color: "primary.contrastText",
                    },
                  }}
                />
              </Tooltip>
            </Box>
          )}
        </Box>

        {/* Comparison Toggle Button */}
        <Box sx={{ mb: 2 }}>
          <Button
            variant="outlined"
            startIcon={<CompareIcon />}
            endIcon={showComparison ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            onClick={handleComparisonToggle}
            disabled={disabled}
            sx={{
              borderColor: 'secondary.main',
              color: 'secondary.main',
              '&:hover': {
                borderColor: 'secondary.dark',
                backgroundColor: 'secondary.light',
                color: 'secondary.contrastText', // Ensure text is readable on hover
              },
              '&:active': {
                backgroundColor: 'secondary.main',
                color: 'secondary.contrastText',
              },
              transition: 'all 0.2s ease-in-out',
            }}
          >
            {showComparison ? 'Hide Comparison' : 'Show Comparison'}
          </Button>
        </Box>

        {/* Comparison Date Range - Collapsible */}
        <Collapse in={showComparison}>
          <Box sx={{ 
            p: 3, 
            border: '2px solid',
            borderColor: 'secondary.light',
            borderRadius: 2,
            backgroundColor: 'secondary.50',
            mb: 2,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: -1,
              left: -1,
              right: -1,
              bottom: -1,
              borderRadius: 2,
              background: 'linear-gradient(45deg, secondary.light, secondary.main)',
              zIndex: -1,
              opacity: 0.3,
            }
          }}>
            <Typography variant="h6" gutterBottom sx={{ 
              color: 'secondary.main', 
              fontWeight: 700,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 2
            }}>
              <CompareIcon fontSize="small" />
              Comparison Date Range
            </Typography>
            <FormControl fullWidth size="small" disabled={disabled}>
              <InputLabel id="comparison-date-range-select-label">Comparison Range</InputLabel>
              <Select
                labelId="comparison-date-range-select-label"
                id="comparison-date-range-select"
                value={comparisonRange?.label || ""}
                label="Comparison Range"
                onChange={handleComparisonPresetChange}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: 'secondary.main',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: 'secondary.main',
                    },
                  },
                }}
              >
                {presetOptions.map((option) => (
                  <MenuItem key={`comparison-${option.label}`} value={option.label}>
                    {option.label}
                  </MenuItem>
                ))}
                <MenuItem value="Custom Range">
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <CalendarIcon fontSize="small" />
                    Custom Range
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            {/* Display comparison custom range label if selected */}
            {comparisonRange?.label === "Custom Range" && (
              <Box sx={{ mt: 2 }}>
                <Tooltip title="Click to edit comparison custom date range">
                  <Chip
                    label={
                      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                        {formatComparisonCustomRangeLabel()}
                        <EditIcon fontSize="small" />
                      </Box>
                    }
                    size="small"
                    color="secondary"
                    variant="outlined"
                    onClick={() => {
                      // Allow clicking the chip to edit the custom range
                      const startDate = new Date(comparisonRange.startDate || "");
                      const endDate = new Date(comparisonRange.endDate || "");
                      setComparisonCustomStartDate(startDate);
                      setComparisonCustomEndDate(endDate);
                      setIsComparisonCustomDialogOpen(true);
                    }}
                    sx={{
                      cursor: "pointer",
                      "&:hover": { 
                        backgroundColor: "secondary.light",
                        color: "secondary.contrastText",
                      },
                    }}
                  />
                </Tooltip>
              </Box>
            )}
          </Box>
        </Collapse>

        {/* Primary Custom Date Range Dialog */}
        <Dialog
          open={isCustomDialogOpen}
          onClose={handleCustomDateCancel}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <CalendarIcon />
              {selectedRange.label === "Custom Range"
                ? "Edit Custom Date Range"
                : "Select Custom Date Range"}
            </Box>
          </DialogTitle>
          <DialogContent>
            <Box
              sx={{ display: "flex", flexDirection: "column", gap: 3, mt: 2 }}
            >
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Start Date
                </Typography>
                <DatePicker
                  value={customStartDate}
                  onChange={(newValue) => setCustomStartDate(newValue)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: "small",
                    },
                  }}
                  maxDate={customEndDate || undefined}
                />
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  End Date
                </Typography>
                <DatePicker
                  value={customEndDate}
                  onChange={(newValue) => setCustomEndDate(newValue)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: "small",
                    },
                  }}
                  minDate={customStartDate || undefined}
                />
              </Box>

              {customStartDate && customEndDate && (
                <Box sx={{ mt: 2 }}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    gutterBottom
                  >
                    Selected Range:
                  </Typography>
                  <Chip
                    label={`${customStartDate.toLocaleDateString()} - ${customEndDate.toLocaleDateString()}`}
                    color="primary"
                    variant="outlined"
                  />
                </Box>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCustomDateCancel}>Cancel</Button>
            <Button
              onClick={handleCustomDateConfirm}
              variant="contained"
              disabled={!customStartDate || !customEndDate}
            >
              Apply
            </Button>
          </DialogActions>
        </Dialog>

        {/* Comparison Custom Date Range Dialog */}
        <Dialog
          open={isComparisonCustomDialogOpen}
          onClose={handleComparisonCustomDateCancel}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <CalendarIcon />
              {comparisonRange?.label === "Custom Range"
                ? "Edit Comparison Custom Date Range"
                : "Select Comparison Custom Date Range"}
            </Box>
          </DialogTitle>
          <DialogContent>
            <Box
              sx={{ display: "flex", flexDirection: "column", gap: 3, mt: 2 }}
            >
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Start Date
                </Typography>
                <DatePicker
                  value={comparisonCustomStartDate}
                  onChange={(newValue) => setComparisonCustomStartDate(newValue)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: "small",
                    },
                  }}
                  maxDate={comparisonCustomEndDate || undefined}
                />
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  End Date
                </Typography>
                <DatePicker
                  value={comparisonCustomEndDate}
                  onChange={(newValue) => setComparisonCustomEndDate(newValue)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: "small",
                    },
                  }}
                  minDate={comparisonCustomStartDate || undefined}
                />
              </Box>

              {comparisonCustomStartDate && comparisonCustomEndDate && (
                <Box sx={{ mt: 2 }}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    gutterBottom
                  >
                    Selected Range:
                  </Typography>
                  <Chip
                    label={`${comparisonCustomStartDate.toLocaleDateString()} - ${comparisonCustomEndDate.toLocaleDateString()}`}
                    color="secondary"
                    variant="outlined"
                  />
                </Box>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleComparisonCustomDateCancel}>Cancel</Button>
            <Button
              onClick={handleComparisonCustomDateConfirm}
              variant="contained"
              disabled={!comparisonCustomStartDate || !comparisonCustomEndDate}
            >
              Apply
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};
