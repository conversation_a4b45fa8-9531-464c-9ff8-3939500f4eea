// Log Filters Component
"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Typography,
  Box,
  Collapse,
  IconButton,
  Chip,
  Divider,
  FormHelperText,
} from "@mui/material";
import {
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon,
} from "@mui/icons-material";
import { LogFilterParams, PAGE_SIZE_OPTIONS } from "@/types/log";

interface LogFiltersProps {
  filters: LogFilterParams;
  onFiltersChange: (filters: LogFilterParams) => void;
  onRefresh: () => void;
  availableActions: { label: string; value: string }[];
  loading?: boolean;
  activeTab?: "audit" | "member_delete";
}

const LogFilters: React.FC<LogFiltersProps> = ({
  filters,
  onFiltersChange,
  onRefresh,
  availableActions,
  loading = false,
  activeTab = "audit",
}) => {
  const [expanded, setExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState<LogFilterParams>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

    const handleFilterChange = (field: keyof LogFilterParams, value: any) => {
    const newFilters = { ...localFilters, [field]: value };
    setLocalFilters(newFilters);
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
  };

  const handleClearFilters = () => {
    const clearedFilters: LogFilterParams = {
      page: 1,
      page_size: localFilters.page_size || 50,
      sort_order: "desc",
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = () => {
    const { page, page_size, sort_order, ...filterFields } = localFilters;
    return Object.values(filterFields).some(
      (value) => value !== undefined && value !== ""
    );
  };

  const getActiveFilterCount = () => {
    const { page, page_size, sort_order, ...filterFields } = localFilters;
    return Object.values(filterFields).filter(
      (value) => value !== undefined && value !== ""
    ).length;
  };

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: expanded ? 2 : 0,
            cursor: "pointer",
          }}
          onClick={() => setExpanded(!expanded)}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <FilterIcon color="primary" />
            <Typography variant="h6">Filters</Typography>
            {hasActiveFilters() && (
              <Chip
                label={`${getActiveFilterCount()} active`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
          <Box sx={{ display: "flex", gap: 1 }}>
            {hasActiveFilters() && (
              <Button
                size="small"
                variant="outlined"
                onClick={handleClearFilters}
                startIcon={<ClearIcon />}
                disabled={loading}
              >
                Clear All
              </Button>
            )}
            <IconButton size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        <Collapse in={expanded}>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            {/* Basic Filters */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Purpose (Search)"
                value={localFilters.purpose || ""}
                onChange={(e) => handleFilterChange("purpose", e.target.value)}
                size="small"
                placeholder="Search in purpose field..."
              />
            </Grid>

            {/* Action Type Filter - Only show for audit tab */}
            {activeTab === "audit" && (
              <Grid item xs={12} md={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Action Type</InputLabel>
                  <Select
                    value={localFilters.action || ""}
                    label="Action Type"
                    onChange={(e) =>
                      handleFilterChange("action", e.target.value)
                    }
                  >
                    <MenuItem value="">All Actions</MenuItem>
                    {availableActions
                      .filter((action) => action.value !== "member_delete")
                      .map(({ label, value }) => (
                        <MenuItem key={value} value={value}>
                          {label}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            {/* Sort Order */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Sort Order</InputLabel>
                <Select
                  value={localFilters.sort_order || "desc"}
                  label="Sort Order"
                  onChange={(e) =>
                    handleFilterChange("sort_order", e.target.value)
                  }
                >
                  <MenuItem value="asc">Ascending (Oldest First)</MenuItem>
                  <MenuItem value="desc">Descending (Newest First)</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Date Range Filters */}
            <Grid item xs={12}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 600, color: "text.secondary" }}
              >
                Date Range
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Start Date & Time"
                type="datetime-local"
                value={
                  localFilters.start_timestamp
                    ? new Date(localFilters.start_timestamp)
                        .toISOString()
                        .slice(0, 16)
                    : ""
                }
                onChange={(e) => {
                  const value = e.target.value;
                  const isoString = value ? new Date(value).toISOString() : "";
                  handleFilterChange("start_timestamp", isoString);
                }}
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="End Date & Time"
                type="datetime-local"
                value={
                  localFilters.end_timestamp
                    ? new Date(localFilters.end_timestamp)
                        .toISOString()
                        .slice(0, 16)
                    : ""
                }
                onChange={(e) => {
                  const value = e.target.value;
                  const isoString = value ? new Date(value).toISOString() : "";
                  handleFilterChange("end_timestamp", isoString);
                }}
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", gap: 2, mt: 3 }}>
            <Button
              variant="contained"
              onClick={handleApplyFilters}
              disabled={loading}
            >
              Apply Filters
            </Button>
            <Button variant="outlined" onClick={onRefresh} disabled={loading}>
              Refresh
            </Button>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default LogFilters;
