"use client";

import React, { useState } from "react";
import {
  Box,
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Typography,
  Collapse,
  IconButton,
  Chip,
  Divider,
} from "@mui/material";
import {
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon,
} from "@mui/icons-material";
import { MemberSearchFilters } from "@/types/member";

interface MemberFiltersProps {
  filters: MemberSearchFilters;
  onFiltersChange: (filters: MemberSearchFilters) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
  loading?: boolean;
}

const MEMBERSHIP_TIERS = [
  { value: "lite", label: "Lite" },
  { value: "premium", label: "Premium" },
];

const NAME_FILTER_OPTIONS = [
  { value: "all", label: "All Members" },
  { value: "exclude", label: "Exclude Missing Names" },
  { value: "include", label: "Show Only Missing Names" },
];



export function MemberFilters({
  filters,
  onFiltersChange,
  onApplyFilters,
  onClearFilters,
  loading = false,
}: MemberFiltersProps) {
  const [expanded, setExpanded] = useState(false);

  const handleFilterChange = (key: keyof MemberSearchFilters, value: any) => {
    const updatedFilters = {
      ...filters,
      [key]: value,
    };
    onFiltersChange(updatedFilters);
  };

  const handleApplyFilters = () => {
    onApplyFilters();
  };

  const handleClearFilters = () => {
    const clearedFilters: MemberSearchFilters = {
      search: "",
      id: undefined,
      uuid: "",
      firstName: "",
      lastName: "",
      email: "",
      membershipTier: "",
      organizationName: "",
      organizationCity: "",
      organizationState: "",
      organizationZip: "",
      companySize: "",
      industry: "",
      dateCreatedFrom: "",
      dateCreatedTo: "",
      hasOrganizations: undefined,
      nameFilter: undefined,
    };
    onFiltersChange(clearedFilters);
    onClearFilters();
  };

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    // Handle boolean values properly
    if (key === 'hasOrganizations') {
      return value !== undefined;
    }
    // Handle nameFilter - only count as active if not "all"
    if (key === 'nameFilter') {
      return value && value !== "all";
    }
    // Handle other values (strings, numbers, etc.)
    return value && value !== "";
  });

  const activeFilterCount = Object.entries(filters).filter(([key, value]) => {
    // Handle boolean values properly
    if (key === 'hasOrganizations') {
      return value !== undefined;
    }
    // Handle nameFilter - only count as active if not "all"
    if (key === 'nameFilter') {
      return value && value !== "all";
    }
    // Handle other values (strings, numbers, etc.)
    return value && value !== "";
  }).length;

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box
          onClick={() => setExpanded(!expanded)}
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: expanded ? 2 : 0,
            cursor: "pointer",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <FilterIcon color="primary" />
            <Typography variant="h6">Filters</Typography>
            {hasActiveFilters && (
              <Chip
                label={`${activeFilterCount} active`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
          <Box sx={{ display: "flex", gap: 1 }}>
            {hasActiveFilters && (
              <Button
                size="small"
                variant="outlined"
                onClick={handleClearFilters}
                startIcon={<ClearIcon />}
                disabled={loading}
              >
                Clear All
              </Button>
            )}
            <IconButton size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        <Collapse in={expanded}>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            {/* Search */}
            {/* <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Search"
                value={filters.search || ""}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                placeholder="Search by name, email, or organization..."
                size="small"
              />
            </Grid> */}

            {/* Member Fields */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="UUID"
                value={filters.uuid || ""}
                onChange={(e) => handleFilterChange("uuid", e.target.value)}
                placeholder="Enter UUID to search..."
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Legacy ID"
                value={filters.id?.toString() || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  const numValue =
                    value === "" ? undefined : parseInt(value, 10);
                  handleFilterChange(
                    "id",
                    isNaN(numValue as number) ? undefined : numValue
                  );
                }}
                placeholder="Enter member ID..."
                size="small"
                type="number"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="First Name"
                value={filters.firstName || ""}
                onChange={(e) =>
                  handleFilterChange("firstName", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={filters.lastName || ""}
                onChange={(e) => handleFilterChange("lastName", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                value={filters.email || ""}
                onChange={(e) => handleFilterChange("email", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Membership Tier</InputLabel>
                <Select
                  value={filters.membershipTier || ""}
                  onChange={(e) =>
                    handleFilterChange("membershipTier", e.target.value)
                  }
                  label="Membership Tier"
                >
                  <MenuItem value="">All Tiers</MenuItem>
                  {MEMBERSHIP_TIERS.map((tier) => (
                    <MenuItem key={tier.value} value={tier.value}>
                      {tier.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Name Filter</InputLabel>
                <Select
                  value={filters.nameFilter || "all"}
                  onChange={(e) =>
                    handleFilterChange("nameFilter", e.target.value)
                  }
                  label="Name Filter"
                >
                  {NAME_FILTER_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Organization Fields */}
            <Grid item xs={12}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 600, color: "text.secondary" }}
              >
                Organization Filters
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Organization Name"
                value={filters.organizationName || ""}
                onChange={(e) =>
                  handleFilterChange("organizationName", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="City"
                value={filters.organizationCity || ""}
                onChange={(e) =>
                  handleFilterChange("organizationCity", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="State"
                value={filters.organizationState || ""}
                onChange={(e) =>
                  handleFilterChange("organizationState", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="ZIP Code"
                value={filters.organizationZip || ""}
                onChange={(e) =>
                  handleFilterChange("organizationZip", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <Select
                  value={
                    filters.hasOrganizations !== undefined
                      ? filters.hasOrganizations.toString()
                      : ""
                  }
                  onChange={(e) => {
                    const value = e.target.value;
                    handleFilterChange(
                      "hasOrganizations",
                      value === "" ? undefined : value === "true"
                    );
                  }}
                  displayEmpty
                  renderValue={(value) => {
                    if (value === "") return "All Members";
                    if (value === "true") return "Has Organizations";
                    if (value === "false") return "No Organizations";
                    return "All Members";
                  }}
                >
                  <MenuItem value="">All Members</MenuItem>
                  <MenuItem value="true">Has Organizations</MenuItem>
                  <MenuItem value="false">No Organizations</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Company Size</InputLabel>
                <Select
                  value={filters.companySize || ""}
                  onChange={(e) =>
                    handleFilterChange("companySize", e.target.value)
                  }
                  label="Company Size"
                >
                  <MenuItem value="">All Sizes</MenuItem>
                  {COMPANY_SIZES.map((size) => (
                    <MenuItem key={size.value} value={size.value}>
                      {size.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid> */}

            {/* <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Industry"
                value={filters.industry || ""}
                onChange={(e) => handleFilterChange("industry", e.target.value)}
                size="small"
              />
            </Grid> */}

            {/* Date Range */}
            <Grid item xs={12}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 600, color: "text.secondary" }}
              >
                Date Range Filter According to Member Creation
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Created From"
                type="date"
                value={filters.dateCreatedFrom || ""}
                onChange={(e) =>
                  handleFilterChange("dateCreatedFrom", e.target.value)
                }
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Created To"
                type="date"
                value={filters.dateCreatedTo || ""}
                onChange={(e) =>
                  handleFilterChange("dateCreatedTo", e.target.value)
                }
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", gap: 2, mt: 3 }}>
            <Button
              variant="contained"
              onClick={handleApplyFilters}
              disabled={loading}
            >
              Apply Filters
            </Button>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
}
