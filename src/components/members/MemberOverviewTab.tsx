"use client";

import React, { useState } from "react";
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from "@mui/material";
import {
  Email as EmailIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  VerifiedUser as VerifyIcon,
  Block as BlockIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Edit as EditIcon,
} from "@mui/icons-material";
import { MemberWithRelations } from "@/types/member";
import { membersService } from "@/services/members";
import MemberStatusChip from "./MemberStatusChip";

interface MemberOverviewTabProps {
  member: MemberWithRelations;
  onMemberUpdate: () => void;
}

const MemberOverviewTab: React.FC<MemberOverviewTabProps> = ({
  member,
  onMemberUpdate,
}) => {
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState(member.communityStatus);
  const [statusReason, setStatusReason] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    let normalizedDateString = dateString;

    if (!/[Z+-]/.test(dateString.slice(-1))) {
      normalizedDateString = dateString.split(".")[0] + "Z";
    }

    const utcDate = new Date(normalizedDateString);

    return utcDate.toLocaleString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true, // 12-hour format
    });
  };

  const handleStatusChange = async () => {
    try {
      setLoading(true);
      setError(null);
      await membersService.updateMemberStatus(member.uuid, newStatus);
      setStatusDialogOpen(false);
      onMemberUpdate();
    } catch (err: any) {
      setError(err.message || "Failed to update status");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ px: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3} height={"100%"} >
        {/* Profile Information */}
        <Grid item xs={12} md={6} height={"100%"} >
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Profile Information
              </Typography>
              <List dense>
              
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Name"
                    secondary={`${member.firstName} ${member.lastName}`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <EmailIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Login email"
                    secondary={
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        {member.loginEmail}
                        {member.loginEmailVerified && (
                          <CheckIcon
                            sx={{ fontSize: "1rem", color: "success.main" }}
                          />
                        )}
                      </Box>
                    }
                  />
                </ListItem>
                {member.personalBusinessEmail && (
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Personal email"
                      secondary={member.personalBusinessEmail}
                    />
                  </ListItem>
                )}
                {member.phone && (
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText primary="Phone" secondary={member.phone} />
                  </ListItem>
                )}
                {member.professionalTitle && (
                  <ListItem>
                    <ListItemIcon>
                      <BusinessIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Professional Title"
                      secondary={member.professionalTitle}
                    />
                  </ListItem>
                )}
                <ListItem>
                  <ListItemIcon>
                    <CalendarIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Member Since"
                    secondary={formatDate(member.dateCreated)}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Membership Information */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Membership Information
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Membership Tier"
                    secondary={
                      <Box component="span" sx={{ display: "inline-block" }}>
                        <MemberStatusChip
                          type="membershipTier"
                          value={member.membershipTier}
                          size="small"
                        />
                      </Box>
                    }
                    secondaryTypographyProps={{ component: "span" }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Community Status"
                    secondary={
                      <Box component="span" sx={{ display: "inline-block" }}>
                        <MemberStatusChip
                          type="communityStatus"
                          value={member.communityStatus}
                          size="small"
                        />
                      </Box>
                    }
                    secondaryTypographyProps={{ component: "span" }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Identity Type"
                    secondary={
                      <Box component="span" sx={{ display: "inline-block" }}>
                        <MemberStatusChip
                          type="identityType"
                          value={member.identityType || "unknown"}
                          size="small"
                        />
                      </Box>
                    }
                    secondaryTypographyProps={{ component: "span" }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Verification Status"
                    secondary={
                      <Box component="span" sx={{ display: "inline-block" }}>
                        <MemberStatusChip
                          type="verificationStatus"
                          value={member.verificationStatus || "unknown"}
                          size="small"
                        />
                      </Box>
                    }
                    secondaryTypographyProps={{ component: "span" }}
                  />
                </ListItem>
                {/* <ListItem>
                  <ListItemText
                    primary="Organizations"
                    secondary={`${
                      member.organizations?.length || 0
                    } associated`}
                  />
                </ListItem> */}

                {/* <ListItem>
                  <ListItemText
                    primary="Awards"
                    secondary={`${member.awards?.length || 0} applications`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Feature Flags"
                    secondary={`${member.featureFlags?.length || 0} enabled`}
                  />
                </ListItem> */}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        {/* <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                <Button
                  variant="outlined"
                  startIcon={<VerifyIcon />}
                  onClick={handleVerifyMember}
                  disabled={loading}
                >
                  Verify Member
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<EmailIcon />}
                  onClick={handleemailVerify}
                  disabled={loading || member.loginEmailVerified}
                >
                  {member.loginEmailVerified
                    ? "email Verified"
                    : "Verify email"}
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => setStatusDialogOpen(true)}
                  disabled={loading}
                >
                  Change Status
                </Button>
                {member.blacklistReport?.isSpam && (
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<WarningIcon />}
                    disabled={loading}
                  >
                    Review Blacklist
                  </Button>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid> */}

        {/* Security Alerts */}
        {member.blacklistReport?.isSpam && (
          <Grid item xs={12}>
            <Alert severity="warning" icon={<WarningIcon />}>
              This member has been flagged for potential spam activity. Review
              their blacklist report in the Security tab.
            </Alert>
          </Grid>
        )}

        {!member.loginEmailVerified && (
          <Grid item xs={12}>
            <Alert severity="info">
              This member's email has not been verified. Consider sending a
              verification email.
            </Alert>
          </Grid>
        )}
      </Grid>

      {/* Status Change Dialog */}
      <Dialog
        open={statusDialogOpen}
        onClose={() => setStatusDialogOpen(false)}
      >
        <DialogTitle>Change Member Status</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>New Status</InputLabel>
              <Select
                value={newStatus}
                label="New Status"
                onChange={(e) =>
                  setNewStatus(
                    e.target.value as
                      | "unverified"
                      | "verified"
                      | "pending"
                      | "rejected"
                  )
                }
              >
                <MenuItem value="unverified">Unverified</MenuItem>
                <MenuItem value="verified">Verified</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="rejected">Rejected</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Reason (optional)"
              multiline
              rows={3}
              value={statusReason}
              onChange={(e) => setStatusReason(e.target.value)}
              placeholder="Provide a reason for the status change..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleStatusChange}
            variant="contained"
            disabled={loading}
          >
            Update Status
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MemberOverviewTab;
