"use client";

import React, { useState } from "react";
import {
  TableRow,
  TableCell,
  IconButton,
  Collapse,
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Tooltip,
  Checkbox,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  VerifiedUser as VerifyIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  EmojiEvents as AwardIcon,
  Flag as FlagIcon,
  Security as SecurityIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  CalendarToday as CalendarIcon,
} from "@mui/icons-material";
import {
  Member,
  MemberWithRelations,
  Organization,
  // MemberAward, // REMOVED: No longer used
  // MemberFeatureFlag, // REMOVED: No longer used
} from "@/types/member";
import MemberStatusChip from "./MemberStatusChip";
import { navigate } from "@/utils/routerService";

interface MemberTableRowProps {
  member: MemberWithRelations;
  isSelected: boolean;
  onSelect: (memberId: string) => void;
  onEdit: (member: Member) => void;
  onDelete: (member: Member) => void;
  onView: (member: Member) => void;
  onVerify: (memberId: string) => void;
  onemailVerify: (memberId: string) => void;
  onStatusChange: (memberId: string, status: string) => void;
  memberPermissions?: {
    view?: boolean;
    create?: boolean;
    update?: boolean;
    delete?: boolean;
  };
}

export const MemberTableRow: React.FC<MemberTableRowProps> = ({
  member,
  isSelected,
  onSelect,
  onEdit,
  onDelete,
  onView,
  onVerify,
  onemailVerify,
  onStatusChange,
  memberPermissions = { update: true, delete: true },
}) => {
  const [expanded, setExpanded] = useState(false);

  const handleExpand = () => {
    setExpanded(!expanded);
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.charAt(0) || ""}${
      lastName?.charAt(0) || ""
    }`.toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getverificationStatus = () => {
    if (member.verifiedData) {
      return member.verifiedData.verificationStatus;
    }
    return "not_started";
  };

  const getBlacklistStatus = () => {
    if (member.blacklistReport) {
      return member.blacklistReport.isSpam ? "blocked" : "clean";
    }
    return "unknown";
  };

  return (
    <>
      <TableRow hover selected={isSelected}>
        <TableCell padding="checkbox">
          <Checkbox
            checked={isSelected}
            onChange={() => onSelect(member.uuid)}
            size="small"
          />
        </TableCell>

        {/* ID */}
        <TableCell>
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: "0.75rem", md: "0.875rem" },
              fontFamily: "monospace",
              fontWeight: 500,
            }}
          >
            {member.id}
          </Typography>
        </TableCell>

        {/* UUID */}
        <TableCell>
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: "0.75rem", md: "0.875rem" },
              fontFamily: "monospace",
              wordBreak: "break-all",
            }}
          >
            {member.uuid}
          </Typography>
        </TableCell>

        {/* Name */}
        <TableCell>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: { xs: 1, md: 2 },
            }}
          >
            <Avatar
              sx={{
                width: { xs: 28, md: 32 },
                height: { xs: 28, md: 32 },
                fontSize: { xs: "0.75rem", md: "0.875rem" },
              }}
            >
              {getInitials(member.firstName, member.lastName)}
            </Avatar>
            <Box>
              <Typography
                variant="body2"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: "0.75rem", md: "0.875rem" },
                }}
              >
                {(member.firstName || '') + " " + (member.lastName || '')}
              </Typography>
              {member.professionalTitle && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ fontSize: { xs: "0.625rem", md: "0.75rem" } }}
                >
                  {member.professionalTitle}
                </Typography>
              )}
            </Box>
          </Box>
        </TableCell>

        {/* email */}
        <TableCell>
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: "0.75rem", md: "0.875rem" },
              wordBreak: "break-word",
            }}
          >
            {member.loginEmail}
          </Typography>
          {member.loginEmailVerified && (
            <Chip
              size="small"
              label="Verified"
              color="success"
              variant="outlined"
              sx={{ fontSize: { xs: "0.625rem", md: "0.75rem" } }}
            />
          )}
        </TableCell>
        {/* Membership Tier */}
        <TableCell align="center">
          <MemberStatusChip
            type="membershipTier"
            value={member.membershipTier}
            size="small"
          />
        </TableCell>

        {/* Community Status */}
        <TableCell align="center">
          <MemberStatusChip
            type="communityStatus"
            value={member?.communityStatus?.toLocaleLowerCase()}
            size="small"
          />
        </TableCell>

        {/* Verification Status */}
        <TableCell align="center">
          <MemberStatusChip
            type="verificationStatus"
            value={member.verificationStatus || "pending"}
            size="small"
          />
        </TableCell>

        {/* Created Date */}
        <TableCell align="center">
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: "0.75rem", md: "0.875rem" },
              color: "text.secondary",
            }}
          >
            {member.dateCreated
              ? new Date(member.dateCreated).toLocaleDateString()
              : "N/A"}
          </Typography>
        </TableCell>

        {/* Updated Date */}
        <TableCell align="center">
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: "0.75rem", md: "0.875rem" },
              color: "text.secondary",
            }}
          >
            {member.dateUpdated
              ? new Date(member.dateUpdated).toLocaleDateString()
              : "N/A"}
          </Typography>
        </TableCell>

        {/* Organization Count */}
        {/* <TableCell align="center">
          <Chip
            label={`${member.organizations?.length || 0} orgs`}
            size="small"
            variant="outlined"
            color="primary"
          />
        </TableCell> */}

        {/* Actions */}
        <TableCell align="center">
          <Box
            sx={{
              display: "flex",
              gap: { xs: 0.25, md: 0.5 },
              flexWrap: "wrap",
              justifyContent: "center",
            }}
          >
            <Tooltip title="View Details">
              <IconButton
                size="small"
                onClick={() => navigate(`/members/${member.uuid}`)}
                sx={{
                  padding: { xs: "4px", md: "8px" },
                  "& .MuiSvgIcon-root": {
                    fontSize: { xs: "1rem", md: "1.25rem" },
                  },
                }}
              >
                <ViewIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit Member">
              <IconButton
                size="small"
                onClick={() => onEdit(member)}
                sx={{
                  padding: { xs: "4px", md: "8px" },
                  "& .MuiSvgIcon-root": {
                    fontSize: { xs: "1rem", md: "1.25rem" },
                  },
                }}
                disabled={!memberPermissions.update}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            {/* <Tooltip title="Verify Member">
              <IconButton
                size="small"
                onClick={() => onVerify(member.uuid)}
                sx={{
                  padding: { xs: "4px", md: "8px" },
                  "& .MuiSvgIcon-root": {
                    fontSize: { xs: "1rem", md: "1.25rem" },
                  },
                }}
              >
                <VerifyIcon fontSize="small" />
              </IconButton>
            </Tooltip> */}
            <Tooltip title="Delete Member">
              <IconButton
                size="small"
                onClick={() => onDelete(member)}
                sx={{
                  padding: { xs: "4px", md: "8px" },
                  "& .MuiSvgIcon-root": {
                    fontSize: { xs: "1rem", md: "1.25rem" },
                  },
                }}
                disabled={!memberPermissions.delete}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title={expanded ? "Collapse Details" : "Expand Details"}>
              <IconButton
                size="small"
                onClick={handleExpand}
                sx={{
                  padding: { xs: "4px", md: "8px" },
                  "& .MuiSvgIcon-root": {
                    fontSize: { xs: "1rem", md: "1.25rem" },
                  },
                }}
              >
                {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </TableCell>
      </TableRow>

      {/* Expanded Details */}
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={11}>
          <Collapse in={expanded} timeout="auto" unmountOnExit>
            <Box sx={{ margin: { xs: 1, md: 2 } }}>
              <Grid container spacing={{ xs: 2, md: 3 }}>
                {/* Member Details */}
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ p: { xs: 1.5, md: 2 } }}>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{ fontSize: { xs: "1rem", md: "1.25rem" } }}
                      >
                        Member Details
                      </Typography>
                      <Grid container spacing={{ xs: 1, md: 2 }}>
                        <Grid item xs={12}>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                          >
                            UUID
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              fontSize: { xs: "0.75rem", md: "0.875rem" },
                              fontFamily: "monospace",
                              wordBreak: "break-all",
                            }}
                          >
                            {member.uuid}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          {/* <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                          >
                            Blacklist Status
                          </Typography>
                          <MemberStatusChip
                            type="blacklistStatus"
                            value={getBlacklistStatus()}
                            size="small"
                          /> */}
                        </Grid>
                        <Grid item xs={12}>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                          >
                            Phone
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                          >
                            {member.phone || "Not provided"}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                          >
                            Personal Email
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                          >
                            {member.personalBusinessEmail || "Not provided"}
                          </Typography>
                        </Grid>
                      </Grid>

                      {/* <Box
                        sx={{
                          mt: 2,
                          display: "flex",
                          gap: 1,
                          flexDirection: { xs: "column", sm: "row" },
                        }}
                      >
                        <Button
                          size="small"
                          startIcon={<EmailIcon />}
                          onClick={() => onemailVerify(member.uuid)}
                          variant="outlined"
                          sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                        >
                          Verify email
                        </Button>
                        <Button
                          size="small"
                          startIcon={<SecurityIcon />}
                          onClick={() =>
                            onStatusChange(member.uuid, "verified")
                          }
                          variant="outlined"
                          sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                        >
                          Approve Member
                        </Button>
                      </Box> */}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Organizations */}
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ p: { xs: 1.5, md: 2 } }}>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{ fontSize: { xs: "1rem", md: "1.25rem" } }}
                      >
                        Organizations ({member.organizations?.length || 0})
                      </Typography>
                      {member.organizations &&
                      member.organizations.length > 0 ? (
                        <List dense>
                          {member.organizations.map((org: Organization) => (
                            <ListItem key={org.id}>
                              <ListItemIcon>
                                <BusinessIcon
                                  sx={{
                                    fontSize: { xs: "1rem", md: "1.25rem" },
                                  }}
                                />
                              </ListItemIcon>
                              <ListItemText
                                primary={org.name || "Unnamed Organization"}
                                secondary={`${org.city || ""} ${
                                  org.state || ""
                                }`}
                                sx={{
                                  "& .MuiListItemText-primary": {
                                    fontSize: { xs: "0.75rem", md: "0.875rem" },
                                  },
                                  "& .MuiListItemText-secondary": {
                                    fontSize: { xs: "0.625rem", md: "0.75rem" },
                                  },
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                        >
                          No organizations associated
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Awards */}
                {/* <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ p: { xs: 1.5, md: 2 } }}>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{ fontSize: { xs: "1rem", md: "1.25rem" } }}
                      >
                        Awards ({member.awards?.length || 0})
                      </Typography>
                      {member.awards && member.awards.length > 0 ? (
                        <List dense>
                          {member.awards.map(
                            (award: MemberAward, index: number) => (
                              <ListItem key={index}>
                                <ListItemIcon>
                                  <AwardIcon
                                    sx={{
                                      fontSize: { xs: "1rem", md: "1.25rem" },
                                    }}
                                  />
                                </ListItemIcon>
                                <ListItemText
                                  primary={`Award #${award.awardListingElementId}`}
                                  secondary={`Status: ${
                                    award.status
                                  } | Qualified: ${
                                    award.isQualified ? "Yes" : "No"
                                  }`}
                                  sx={{
                                    "& .MuiListItemText-primary": {
                                      fontSize: {
                                        xs: "0.75rem",
                                        md: "0.875rem",
                                      },
                                    },
                                    "& .MuiListItemText-secondary": {
                                      fontSize: {
                                        xs: "0.625rem",
                                        md: "0.75rem",
                                      },
                                    },
                                  }}
                                />
                              </ListItem>
                            )
                          )}
                        </List>
                      ) : (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                        >
                          No awards applied for
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid> */}

                {/* Feature Flags */}
                {/* <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ p: { xs: 1.5, md: 2 } }}>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{ fontSize: { xs: "1rem", md: "1.25rem" } }}
                      >
                        Feature Flags ({member.featureFlags?.length || 0})
                      </Typography>
                      {member.featureFlags && member.featureFlags.length > 0 ? (
                        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                          {member.featureFlags.map(
                            (flag: MemberFeatureFlag) => (
                              <Chip
                                key={flag.id}
                                label={flag.featureHandle}
                                color={flag.enabled ? "success" : "default"}
                                size="small"
                                variant="outlined"
                                sx={{
                                  fontSize: { xs: "0.625rem", md: "0.75rem" },
                                }}
                              />
                            )
                          )}
                        </Box>
                      ) : (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                        >
                          No feature flags assigned
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid> */}
              </Grid>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
};

export default MemberTableRow;
