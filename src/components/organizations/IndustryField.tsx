import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  TextField,
  Box,
  Typography,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Autocomplete,
  Button,
} from "@mui/material";
import { Add as AddIcon } from "@mui/icons-material";
import { industryService } from "@/services/industryService";
import { Industry } from "@/types/industry";
import { organizationsActions } from "@/store/organizations/redux";

interface IndustryFieldProps {
  value: string;
  onChange: (value: string) => void;
  onBlur: () => void;
  error?: string;
  disabled?: boolean;
  label?: string;
}

const IndustryField: React.FC<IndustryFieldProps> = ({
  value,
  onChange,
  onBlur,
  error,
  disabled = false,
  label = "Industry",
}) => {
  const dispatch = useDispatch();
  const [industries, setIndustries] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [creating, setCreating] = useState(false);
  const [inputValue, setInputValue] = useState(value || "");

  // Load industries on component mount
  useEffect(() => {
    const loadIndustries = async () => {
      setLoading(true);
      setLoadError(null);
      try {
        const response = await industryService.getIndustries({ size: 100 });
        if (response?.industries && Array.isArray(response.industries)) {
          const options = response.industries
            .map((industry: Industry) => industry.name)
            .filter((name: string) => name && name.trim() !== "");
          dispatch(organizationsActions.industriesSuccess(options.length));
          setIndustries(options);
        } else {
          throw new Error("Invalid response format");
        }
      } catch (err) { 
        setLoadError("Failed to load industries");
        console.error("Error loading industries:", err);
      } finally {
        setLoading(false);
      }
    };

    loadIndustries();
  }, []);

  // Update inputValue when value prop changes
  useEffect(() => {
    setInputValue(value || "");
  }, [value]);

  // Add current value to options if it's not already there
  const allOptions = React.useMemo(() => {
    const options = [...industries];
    if (value && value.trim() !== "" && !options.includes(value)) {
      options.push(value);
    }
    return options.sort();
  }, [industries, value]);

  // Prepare options for Autocomplete (including "add_new")
  const autocompleteOptions = React.useMemo(() => {
    if (loading) return [];
    if (loadError) return ["add_new"];
    return ["", ...allOptions, "add_new"];
  }, [loading, loadError, allOptions]);

  const handleCreateIndustry = async (
    event: React.FormEvent<HTMLFormElement>
  ) => {
    event.preventDefault();
    event.stopPropagation();
    const formData = new FormData(event.currentTarget);
    const industryName = formData.get("name") as string;
    const description = formData.get("description") as string;

    if (!industryName?.trim()) return;

    // Check for duplicates
    const exists = allOptions.some(
      (option) => option.toLowerCase() === industryName.trim().toLowerCase()
    );

    if (exists) {
      return;
    }

    setCreating(true);
    try {
      await industryService.createIndustry({
        name: industryName.trim(),
        description: description?.trim() || undefined,
      });

      // Update the form value
      onChange(industryName.trim());

      // Add to local list
      setIndustries((prev) => [...prev, industryName.trim()].sort());

      // Update industries count in Redux
      dispatch(organizationsActions.industriesSuccess(industries.length + 1));

      // Close dialog and reset form
      setShowAddDialog(false);
      event.currentTarget.reset();
    } catch (error) {
      console.error("Failed to create industry:", error);
    } finally {
      setCreating(false);
    }
  };

  const handleDialogClose = () => {
    if (!creating) {
      setShowAddDialog(false);
    }
  };

  const handleAutocompleteChange = (event: any, newValue: string | null) => {
    if (newValue === "add_new") {
      setShowAddDialog(true);
    } else if (newValue !== null) {
      // Handle both selection and free text input
      const finalValue = newValue || "";
      onChange(finalValue);
    }
  };

  const handleInputChange = (event: any, newInputValue: string) => {
    setInputValue(newInputValue);
    // Update form value for free text input
    onChange(newInputValue);
  };

  const handleBlur = () => {
    onBlur();
  };

  return (
    <Box>
      <Autocomplete
        value={value || ""}
        inputValue={inputValue}
        options={autocompleteOptions}
        freeSolo
        disabled={disabled || loading}
        onChange={handleAutocompleteChange}
        onInputChange={handleInputChange}
        onBlur={handleBlur}
        renderInput={(params) => (
          <TextField
            {...params}
            label={label}
            placeholder="Type or select an industry"
            error={!!error}
            helperText={error}
            disabled={disabled || loading}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {loading && <CircularProgress size={20} />}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        renderOption={(props, option) => {
          if (option === "add_new") {
            return (
              <Box component="li" {...props}>
                <Box display="flex" alignItems="center" gap={1}>
                  <AddIcon fontSize="small" />
                  <Typography variant="body2">Add New Industry</Typography>
                </Box>
              </Box>
            );
          }
          if (option === "") {
            return (
              <Box component="li" {...props}>
                <em>Select an industry</em>
              </Box>
            );
          }
          return (
            <Box component="li" {...props}>
              {option}
            </Box>
          );
        }}
        noOptionsText={
          loadError ? "Error loading industries" : "No industries found"
        }
      />

      {loadError && !error && (
        <Box mt={1}>
          <Typography variant="caption" color="error" sx={{ mr: 1 }}>
            {loadError}
          </Typography>
          <Button
            size="small"
            onClick={() => window.location.reload()}
            sx={{ minWidth: "auto", p: 0.5 }}
          >
            Retry
          </Button>
        </Box>
      )}

      {/* Add New Industry Dialog */}
      <Dialog
        open={showAddDialog}
        onClose={handleDialogClose}
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown={creating}
        onClick={(e) => e.stopPropagation()}
        onKeyDown={(e) => {
          if (e.key === "Enter" && e.target !== e.currentTarget) {
            e.stopPropagation();
          }
        }}
      >
        <DialogTitle>Add New Industry</DialogTitle>
        <form
          onSubmit={handleCreateIndustry}
          onClick={(e) => e.stopPropagation()}
        >
          <DialogContent>
            <Box sx={{ pt: 1 }}>
              {creating && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  Creating industry...
                </Alert>
              )}
              <TextField
                fullWidth
                name="name"
                label="Industry Name"
                margin="normal"
                required
                autoFocus
                disabled={creating}
              />
              <TextField
                fullWidth
                name="description"
                label="Description (Optional)"
                margin="normal"
                multiline
                rows={3}
                disabled={creating}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogClose} disabled={creating}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={creating}
              startIcon={
                creating ? <CircularProgress size={16} /> : <AddIcon />
              }
            >
              {creating ? "Creating..." : "Create Industry"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default IndustryField;
