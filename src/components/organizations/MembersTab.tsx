// Members Tab Component
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Visibility as VisibilityIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Person as PersonIcon,
} from "@mui/icons-material";

// Services
import { organizationService } from "../../services/organizationService";

// Types
import { Organization, OrganizationMember } from "../../types/organization";

interface MembersTabProps {
  organization: Organization;
}

const MembersTab: React.FC<MembersTabProps> = ({ organization }) => {
  const router = useRouter();
  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Fetch members on component mount and when page/rowsPerPage changes
  useEffect(() => {
    const fetchMembers = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await organizationService.getOrganizationMembers(
          organization.uuid,
          {
            perPage: page + 1, // API uses 1-based pagination
            pageSize: rowsPerPage,
          }
        );

        setMembers(response.members);
        setTotalCount(response.pagination.totalCount);
      } catch (err) {
        console.error("Error fetching organization members:", err);
        setError("Failed to load organization members. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchMembers();
  }, [organization.uuid, page, rowsPerPage]);

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle member click - redirect to member view page
  const handleMemberClick = (memberUuid: string) => {
    router.push(`/members/${memberUuid}`);
  };

  // Format phone number
  const formatPhoneNumber = (phone: string) => {
    if (!phone) return "Not provided";
    return phone;
  };

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="200px"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" sx={{ mb: 1 }}>
          Organization Members
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {totalCount} member{totalCount !== 1 ? "s" : ""} found
        </Typography>
      </Box>

      {/* Members Table */}
      <Paper sx={{ width: "100%", overflow: "hidden" }}>
        <TableContainer>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600 }}>UUID</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Name</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Email</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Phone</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Username</TableCell>
                <TableCell sx={{ fontWeight: 600 }} align="center">
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {members.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                    <Typography variant="body2" color="text.secondary">
                      No members found for this organization
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                members.map((member) => (
                  <TableRow
                    key={member.uuid}
                    hover
                    sx={{ cursor: "pointer" }}
                    onClick={() => handleMemberClick(member.uuid)}
                  >
                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{
                          fontSize: { xs: "0.75rem", md: "0.875rem" },
                          fontFamily: "monospace",
                          wordBreak: "break-all",
                        }}
                      >
                        {member.uuid}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <PersonIcon sx={{ mr: 1, color: "primary.main" }} />
                        <Box>
                          <Typography variant="body1" sx={{ fontWeight: 500 }}>
                            {member.name}
                          </Typography>
                          {member.lastName && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              {member.lastName}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <EmailIcon sx={{ mr: 1, fontSize: "small" }} />
                        <Typography variant="body2">
                          {member.email || "Not provided"}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <PhoneIcon sx={{ mr: 1, fontSize: "small" }} />
                        <Typography variant="body2">
                          {formatPhoneNumber(member.phoneNumber)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={member.username}
                        size="small"
                        variant="outlined"
                        color="primary"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="View Member Details">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMemberClick(member.uuid);
                          }}
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Box>
  );
};

export default MembersTab;
