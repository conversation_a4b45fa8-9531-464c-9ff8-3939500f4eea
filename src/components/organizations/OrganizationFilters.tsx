// Organization Filters Component
"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Typography,
  Box,
  Collapse,
  IconButton,
  Chip,
  Divider,
} from "@mui/material";
import {
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon,
} from "@mui/icons-material";
import { OrganizationFilterParams } from "@/types/organization";
import IndustryField from "./IndustryField";

interface OrganizationFiltersProps {
  filters: OrganizationFilterParams;
  onFiltersChange: (filters: OrganizationFilterParams) => void;
  onRefresh: () => void;
  loading?: boolean;
}

// Industry options will be loaded from API

// Company size options
const COMPANY_SIZE_OPTIONS = [
  { value: "Small (1-50)", label: "Small (1-50)" },
  { value: "Medium (51-250)", label: "Medium (51-250)" },
  { value: "Large (251-1000)", label: "Large (251-1000)" },
  { value: "Enterprise (1000+)", label: "Enterprise (1000+)" },
];

// Sort options
const SORT_OPTIONS = [
  { value: "dateUpdated", label: "Date Updated" },
  { value: "dateCreated", label: "Date Created" },
  { value: "name", label: "Name" },
  // { value: "city", label: "City" },
  // { value: "state", label: "State" },
  // { value: "industry", label: "Industry" },
  { value: "yearFounded", label: "Year Founded" },
  // { value: "annualRevenue", label: "Annual Revenue" },
];

const OrganizationFilters: React.FC<OrganizationFiltersProps> = ({
  filters,
  onFiltersChange,
  onRefresh,
  loading = false,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [localFilters, setLocalFilters] =
    useState<OrganizationFilterParams>(filters);
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (
    field: keyof OrganizationFilterParams,
    value: any
  ) => {
    const newFilters = { ...localFilters, [field]: value };
    setLocalFilters(newFilters);
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
  };

  const handleClearFilters = () => {
    const clearedFilters: OrganizationFilterParams = {
      page: 1,
      pageSize: localFilters.pageSize || 10,
      sortBy: "dateUpdated",
      sortOrder: "desc",
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = () => {
    const { page, pageSize, sortBy, sortOrder, ...filterFields } = localFilters;
    return Object.values(filterFields).some(
      (value) => value !== undefined && value !== ""
    );
  };

  const getActiveFilterCount = () => {
    const { page, pageSize, sortBy, sortOrder, ...filterFields } = localFilters;
    return Object.values(filterFields).filter(
      (value) => value !== undefined && value !== ""
    ).length;
  };

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: expanded ? 2 : 0,
            cursor: "pointer",
          }}
          onClick={() => setExpanded(!expanded)}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <FilterIcon color="primary" />
            <Typography variant="h6">Filters</Typography>
            {hasActiveFilters() && (
              <Chip
                label={`${getActiveFilterCount()} active`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
          <Box sx={{ display: "flex", gap: 1 }}>
            {hasActiveFilters() && (
              <Button
                size="small"
                variant="outlined"
                onClick={handleClearFilters}
                startIcon={<ClearIcon />}
                disabled={loading}
              >
                Clear All
              </Button>
            )}
            <IconButton size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        <Collapse in={expanded}>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            {/* Basic Organization Fields */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Organization Name"
                value={localFilters.name || ""}
                onChange={(e) => handleFilterChange("name", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                value={localFilters.email || ""}
                onChange={(e) => handleFilterChange("email", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone"
                value={localFilters.phone || ""}
                onChange={(e) => handleFilterChange("phone", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <IndustryField
                value={localFilters.industry || ""}
                onChange={(value) => handleFilterChange("industry", value)}
                onBlur={() => {}}
                disabled={loading}
                label="Industry"
              />
            </Grid>

            {/* Location Filters */}
            <Grid item xs={12}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 600, color: "text.secondary" }}
              >
                Location Filters
              </Typography>
            </Grid>

          
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Year Founded From"
                value={localFilters.yearFoundedMin || ""}
                onChange={(e) =>
                  handleFilterChange("yearFoundedMin", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Year Founded To"
                value={localFilters.yearFoundedMax || ""}
                onChange={(e) =>
                  handleFilterChange("yearFoundedMax", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="City"
                value={localFilters.city || ""}
                onChange={(e) => handleFilterChange("city", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="State"
                value={localFilters.state || ""}
                onChange={(e) => handleFilterChange("state", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="ZIP Code"
                value={localFilters.zip || ""}
                onChange={(e) => handleFilterChange("zip", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Has Members</InputLabel>
                <Select
                  value={
                    localFilters.hasMembers !== undefined
                      ? localFilters.hasMembers.toString()
                      : ""
                  }
                  label="Has Members"
                  onChange={(e) => {
                    const value = e.target.value;
                    handleFilterChange(
                      "hasMembers",
                      value === "" ? undefined : value === "true"
                    );
                  }}
                >
                  <MenuItem value="">All Organizations</MenuItem>
                  <MenuItem value="true">Has Members</MenuItem>
                  <MenuItem value="false">No Members</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Date Range */}
            <Grid item xs={12}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 600, color: "text.secondary" }}
              >
                Date Range
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Created From"
                type="date"
                value={localFilters.dateCreatedFrom || ""}
                onChange={(e) =>
                  handleFilterChange("dateCreatedFrom", e.target.value)
                }
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Created To"
                type="date"
                value={localFilters.dateCreatedTo || ""}
                onChange={(e) =>
                  handleFilterChange("dateCreatedTo", e.target.value)
                }
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>


            {/* <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Sort Order</InputLabel>
                <Select
                  value={localFilters.sortOrder || "desc"}
                  label="Sort Order"
                  onChange={(e) =>
                    handleFilterChange("sortOrder", e.target.value)
                  }
                >
                  <MenuItem value="asc">Ascending</MenuItem>
                  <MenuItem value="desc">Descending</MenuItem>
                </Select>
              </FormControl>
            </Grid> */}
          </Grid>

          <Box sx={{ display: "flex", gap: 2, mt: 3 }}>
            <Button
              variant="contained"
              onClick={handleApplyFilters}
              disabled={loading}
            >
              Apply Filters
            </Button>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default OrganizationFilters;
