// Organization Form Component
import React, { useState, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
} from "@mui/material";
import { Save as SaveIcon, Cancel as CancelIcon } from "@mui/icons-material";

// Redux imports
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../store";
import { organizationsActions } from "../../store/organizations/redux";
import { organizationsSelectors } from "../../store/organizations/selector";

// Types
import {
  Organization,
  CreateOrganizationData,
  UpdateOrganizationData,
} from "../../types/organization";

// Enhanced Validation
import {
  companySizeOptions,
  stateOptions,
} from "../../utils/organizationValidation";

// Components
import IndustryField from "./IndustryField";

interface OrganizationFormProps {
  organization?: Organization;
  mode: "create" | "edit";
  onCancel: () => void;
  onSubmit?: () => void;
}

const OrganizationForm: React.FC<OrganizationFormProps> = ({
  organization,
  mode,
  onCancel,
  onSubmit,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const createLoading = useSelector(organizationsSelectors.selectCreateLoading);
  const updateLoading = useSelector(organizationsSelectors.selectUpdateLoading);
  const loading = mode === "create" ? createLoading : updateLoading;
  const error = useSelector(organizationsSelectors.selectError);

  // State for API validation errors
  const [apiErrors, setApiErrors] = useState<Record<string, string>>({});

  // Formik validation schema
  const validationSchema = Yup.object({
    name: Yup.string()
      .required("Organization name is required")
      .min(2, "Organization name must be at least 2 characters")
      .max(100, "Organization name must be less than 100 characters")
      .trim(),

    email: Yup.string()
      .trim()
      .email("Please enter a valid email address")
      .max(100, "Email must be less than 100 characters")
      .optional(),

    phone: Yup.string()
      .required("Phone number is required")
      .matches(
        /^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/,
        "Please enter a valid phone number"
      )
      .max(20, "Phone number must be less than 20 characters"),

    address1: Yup.string()
      .required("Address is required")
      .min(5, "Address must be at least 5 characters")
      .max(200, "Address must be less than 200 characters")
      .trim(),

    address2: Yup.string()
      .optional()
      .max(200, "Address line 2 must be less than 200 characters")
      .trim(),

    city: Yup.string()
      .required("City is required")
      .min(2, "City must be at least 2 characters")
      .max(100, "City must be less than 100 characters")
      .trim(),

    state: Yup.string()
      .required("State is required")
      .oneOf(stateOptions, "Please select a valid state"),

    zip: Yup.string().required("ZIP code is required"),

    industry: Yup.string().required("Industry is required"),
    annualRevenue: Yup.string()
      .optional()
      .max(50, "Annual revenue must be less than 50 characters")
      .matches(
        /^[\d,]+$/,
        "Please enter a valid number (e.g., 1000000 or 1,000,000)"
      ),

    companySize: Yup.string()
      .optional()
      .oneOf(companySizeOptions, "Please select a valid company size"),

    yearFounded: Yup.string()
      .optional()
      .matches(/^\d{4}$/, "Please enter a valid 4-digit year (e.g., 2020)")
      .test(
        "year-range",
        "Year must be between 1800 and current year",
        (value) => {
          if (!value) return true;
          const year = parseInt(value);
          const currentYear = new Date().getFullYear();
          return year >= 1800 && year <= currentYear;
        }
      ),

    businessProfileElementId: Yup.number()
      .optional()
      .nullable()
      .positive("Business profile element ID must be a positive number")
      .integer("Business profile element ID must be an integer"),
  });

  // Formik setup
  const formik = useFormik({
    initialValues: organization || {
      name: "",
      email: "",
      phone: "",
      address1: "",
      address2: "",
      city: "",
      state: "",
      zip: "",
      industry: "",
      annualRevenue: "",
      companySize: "",
      yearFounded: "",
      businessProfileElementId: undefined,
    },
    validationSchema,
    enableReinitialize: true,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: (values) => {
      handleFormSubmit(values);
    },
  });

  // Enhanced API data validation - only run on initial load
  useEffect(() => {
    if (organization && mode === "edit") {
      // Only validate on initial load, not on every render
      const validation = validateApiData(organization);
      if (!validation.isValid) {
        setApiErrors(validation.errors);
      } else {
        setApiErrors({});
      }
    }
  }, [organization, mode]);

  // Clear API errors when user starts editing
  useEffect(() => {
    const handleFieldChange = (fieldName: string) => {
      if (apiErrors[fieldName]) {
        setApiErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[fieldName];
          return newErrors;
        });
      }
    };

    // Watch for formik value changes
    const subscription = formik.values;
    Object.keys(apiErrors).forEach((fieldName) => {
      if (
        formik.values[fieldName as keyof CreateOrganizationData] !==
        organization?.[fieldName as keyof CreateOrganizationData]
      ) {
        handleFieldChange(fieldName);
      }
    });
  }, [formik.values, apiErrors, organization]);

  // Enhanced form submission with validation
  const handleFormSubmit = (data: any) => {
    // Clear previous API errors
    setApiErrors({});

    // Validate data before submission
    const validation = validateApiData(data);
    if (!validation.isValid) {
      setApiErrors(validation.errors);
      return;
    }

    if (mode === "create") {
      dispatch(organizationsActions.createOrganization(data));
    } else if (organization) {
      dispatch(
        organizationsActions.updateOrganization({
          uuid: organization.uuid,
          data: data as UpdateOrganizationData,
        })
      );
    }
  };

  // Handle cancel
  const handleCancel = () => {
    formik.resetForm();
    setApiErrors({});
    onCancel();
  };

  // Enhanced field rendering with error styling
  const renderTextField = (
    name: keyof CreateOrganizationData,
    label: string,
    placeholder: string,
    type: string = "text"
  ) => {
    const fieldError = formik.errors[name];
    const apiError = apiErrors[name];
    const hasError = !!(fieldError || apiError);
    const errorMessage = apiError || fieldError;

    return (
      <TextField
        fullWidth
        id={name}
        name={name}
        label={label}
        placeholder={placeholder}
        type={type}
        value={formik.values[name]}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={hasError}
        helperText={errorMessage}
        disabled={loading}
        sx={
          hasError
            ? {
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "error.main",
                    borderWidth: "2px",
                  },
                  "&:hover fieldset": {
                    borderColor: "error.main",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "error.main",
                  },
                },
              }
            : undefined
        }
      />
    );
  };

  // Enhanced select field rendering
  const renderSelectField = (
    name: keyof CreateOrganizationData,
    label: string,
    options: string[]
  ) => {
    const fieldError = formik.errors[name];
    const apiError = apiErrors[name];
    const hasError = !!(fieldError || apiError);
    const errorMessage = apiError || fieldError;

    return (
      <FormControl
        fullWidth
        error={hasError}
        sx={
          hasError
            ? {
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "error.main",
                    borderWidth: "2px",
                  },
                  "&:hover fieldset": {
                    borderColor: "error.main",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "error.main",
                  },
                },
              }
            : undefined
        }
      >
        <InputLabel>{label}</InputLabel>
        <Select
          id={name}
          name={name}
          value={formik.values[name]}
          label={label}
          disabled={loading}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
        >
          {options.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </Select>
        {errorMessage && (
          <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
            {errorMessage}
          </Typography>
        )}
      </FormControl>
    );
  };

  // Calculate if form is actually valid
  const isFormValid = formik.isValid && Object.keys(apiErrors).length === 0;

  // Additional validation check for required fields
  const hasRequiredFields =
    formik.values.name &&
    formik.values.phone &&
    formik.values.address1 &&
    formik.values.city &&
    formik.values.state &&
    formik.values.zip &&
    formik.values.industry;

  // Final validation state
  const finalIsValid =
    hasRequiredFields && formik.isValid && Object.keys(apiErrors).length === 0;

  console.log("Formik Form State:", {
    isValid: formik.isValid,
    apiErrors: Object.keys(apiErrors).length,
    isFormValid,
    finalIsValid,
    isDirty: formik.dirty,
    errors: Object.keys(formik.errors).length,
    hasRequiredFields,
    values: {
      name: !!formik.values.name,
      email: !!formik.values.email, // Optional field
      phone: !!formik.values.phone,
      address1: !!formik.values.address1,
      city: !!formik.values.city,
      state: !!formik.values.state,
      zip: !!formik.values.zip,
      industry: !!formik.values.industry,
      businessProfileElementId: formik.values.businessProfileElementId,
    },
    formikErrors: formik.errors,
  });

  return (
    <Box component="form" onSubmit={formik.handleSubmit}>
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title={`${mode === "create" ? "Create" : "Edit"} Organization`}
          subheader={`${
            mode === "create" ? "Add a new" : "Update"
          } organization to the system`}
        />
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* API Validation Errors Alert */}
      {Object.keys(apiErrors).length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
            Data validation issues detected:
          </Typography>
          <Box component="ul" sx={{ m: 0, pl: 2 }}>
            {Object.entries(apiErrors).map(([field, message]) => (
              <Typography key={field} component="li" variant="body2">
                <strong>{field}:</strong> {message}
              </Typography>
            ))}
          </Box>
        </Alert>
      )}

      {/* Basic Information Section */}
      <Card sx={{ mb: 3 }}>
        <CardHeader title="Basic Information" />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              {renderTextField(
                "name",
                "Organization Name",
                "Enter organization name"
              )}
            </Grid>
            <Grid item xs={12} md={6}>
              {renderTextField(
                "email",
                "Email",
                "Enter email address",
                "email"
              )}
            </Grid>
            <Grid item xs={12} md={6}>
              {renderTextField("phone", "Phone", "Enter phone number")}
            </Grid>
            <Grid item xs={12} md={6}>
              <IndustryField
                value={formik.values.industry}
                onChange={(value) => formik.setFieldValue("industry", value)}
                onBlur={() => formik.setFieldTouched("industry", true)}
                error={formik.errors.industry || apiErrors.industry}
                disabled={loading}
                label="Industry"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              {renderTextField(
                "annualRevenue",
                "Annual Revenue",
                "Enter annual revenue"
              )}
            </Grid>
            <Grid item xs={12} md={4}>
              {renderSelectField(
                "companySize",
                "Company Size",
                companySizeOptions
              )}
            </Grid>
            <Grid item xs={12} md={4}>
              {renderTextField(
                "yearFounded",
                "Year Founded",
                "Enter year founded"
              )}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Address Section */}
      <Card sx={{ mb: 3 }}>
        <CardHeader title="Address Information" />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              {renderTextField(
                "address1",
                "Address Line 1",
                "Enter street address"
              )}
            </Grid>
            <Grid item xs={12}>
              {renderTextField(
                "address2",
                "Address Line 2",
                "Enter apartment, suite, etc."
              )}
            </Grid>
            <Grid item xs={12} md={4}>
              {renderTextField("city", "City", "Enter city")}
            </Grid>
            <Grid item xs={12} md={4}>
              {renderSelectField("state", "State", stateOptions)}
            </Grid>
            <Grid item xs={12} md={4}>
              {renderTextField("zip", "ZIP Code", "Enter ZIP code")}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <Card>
        <CardContent>
          <Box display="flex" gap={2} justifyContent="flex-end">
            <Button
              variant="outlined"
              startIcon={<CancelIcon />}
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              startIcon={
                loading ? <CircularProgress size={20} /> : <SaveIcon />
              }
              disabled={loading || !finalIsValid || !formik.dirty}
            >
              {loading ? "Saving..." : `Save Organization`}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

// Helper function for API data validation
const validateApiData = (data: any) => {
  const errors: Record<string, string> = {};

  // Only validate if data exists and has content
  if (!data) {
    return { isValid: false, errors: { general: "No data provided" } };
  }

  // Check for required fields - only if they're truly missing or empty
  if (!data.name || data.name.trim() === "") {
    errors.name = "Organization name is required";
  }

  if (
    data.email &&
    data.email.trim() !== "" &&
    !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)
  ) {
    errors.email = "Please enter a valid email address";
  }

  if (!data.phone || data.phone.trim() === "") {
    errors.phone = "Phone number is required";
  } else if (
    !/^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/.test(
      data.phone
    )
  ) {
    errors.phone = "Please enter a valid phone number";
  }

  if (!data.address1 || data.address1.trim() === "") {
    errors.address1 = "Address is required";
  }

  if (!data.city || data.city.trim() === "") {
    errors.city = "City is required";
  }

  if (!data.state || data.state.trim() === "") {
    errors.state = "State is required";
  }

  if (!data.zip || data.zip.trim() === "") {
    errors.zip = "ZIP code is required";
  }

  if (!data.industry || data.industry.trim() === "") {
    errors.industry = "Industry is required";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

export default OrganizationForm;
