// Organization List Component with Custom Table
import React, { useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, Typography, Alert } from "@mui/material";
import { useRouter } from "next/navigation";
// Redux imports
import { AppDispatch, RootState } from "../../store";
import { organizationsActions } from "../../store/organizations/redux";
import { organizationsSelectors } from "../../store/organizations/selector";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { ConfirmDialog } from "../ui/ConfirmDialog";
import { Organization } from "@/types/organization";
import CustomTable, { Column } from "../ui/CustomTable";

// Constants
const PAGE_SIZE_OPTIONS = [10, 25, 50];

const OrganizationList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  // Redux state
  const organizations = useSelector(organizationsSelectors.selectOrganizations);
  const loading = useSelector(organizationsSelectors.selectLoading);
  const error = useSelector(organizationsSelectors.selectError);
  const pagination = useSelector(organizationsSelectors.selectPagination);
  const filters = useSelector(
    (state: RootState) => state.organizations.filters
  );
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [organizationToDelete, setOrganizationToDelete] = useState<string>("");

  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Organizations module for current user's role
  let orgPermissions = {
    view: false,
    create: false,
    edit: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const orgModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "organizations"
      );
      if (orgModule) {
        orgPermissions = {
          view: orgModule.view || false,
          create: orgModule.create || false,
          edit: orgModule.update || false, // Map 'update' to 'edit' for the table
          delete: orgModule.delete || false,
        };
      }
    }
  }

  // Handle pagination change
  const handlePageChange = useCallback(
    (newPage: number) => {
      const newFilters = {
        ...filters,
        page: newPage,
      };
      dispatch(organizationsActions.setFilters(newFilters));
      dispatch(organizationsActions.fetchOrganizations(newFilters));
    },
    [dispatch, filters]
  );

  // Handle page size change
  const handlePageSizeChange = useCallback(
    (newPageSize: number) => {
      const newFilters = {
        ...filters,
        page: 1, // Reset to first page when changing page size
        pageSize: newPageSize,
      };
      dispatch(organizationsActions.setFilters(newFilters));
      dispatch(organizationsActions.fetchOrganizations(newFilters));
    },
    [dispatch, filters]
  );

  // Handle sorting change
  const handleSortChange = useCallback(
    (field: string, sort: "asc" | "desc") => {
      const newFilters = {
        ...filters,
        sortBy: field as any,
        sortOrder: sort,
        page: 1, // Reset to first page when sorting changes
      };
      dispatch(organizationsActions.setFilters(newFilters));
      dispatch(organizationsActions.fetchOrganizations(newFilters));
    },
    [dispatch, filters]
  );

  // Handle view organization
  const handleViewOrganization = (row: Organization) => {
    router.push(`/organizations/${row.uuid}`);
  };

  // Handle edit organization
  const handleEditOrganization = (row: Organization) => {
    router.push(`/organizations/${row.uuid}/edit`);
  };

  // Handle delete organization
  const handleDeleteOrganization = (row: Organization) => {
    setShowDeleteDialog(true);
    setOrganizationToDelete(row.uuid);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (organizationToDelete) {
      dispatch(organizationsActions.deleteOrganization(organizationToDelete));
      setShowDeleteDialog(false);
    }
  };

  // Custom table columns
  const columns: Column[] = [
    {
      field: "name",
      headerName: "Name",
      width: 200,
      minWidth: 180,
      sortable: true,
      renderCell: (value: string) => (
        <Typography variant="body2" fontWeight="medium" title={value} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
          {value}
        </Typography>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      width: 200,
      minWidth: 180,
      sortable: true,
      renderCell: (value: string) => (
        <Typography variant="body2" color="text.secondary" title={value} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
          {value}
        </Typography>
      ),
    },
    {
      field: "industry",
      headerName: "Industry",
      width: 150,
      minWidth: 130,
      sortable: true,
      renderCell: (value: string) => (
        <Typography variant="body2" color="text.secondary" title={value} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
          {value}
        </Typography>
      ),
    },
    {
      field: "phone",
      headerName: "Phone",
      width: 150,
      minWidth: 130,
      sortable: true,
      renderCell: (value: string) => (
        <Typography variant="body2" color="text.secondary" title={value} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
          {value}
        </Typography>
      ),
    },
    {
      field: "city",
      headerName: "City",
      width: 120,
      minWidth: 100,
      sortable: true,
      renderCell: (value: string) => (
        <Typography variant="body2" color="text.secondary" title={value} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
          {value}
        </Typography>
      ),
    },
    {
      field: "state",
      headerName: "State",
      width: 100,
      minWidth: 80,
      sortable: true,
      renderCell: (value: string) => (
        <Typography variant="body2" color="text.secondary" title={value} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
          {value}
        </Typography>
      ),
    },
    {
      field: "yearFounded",
      headerName: "Year Founded",
      width: 120,
      minWidth: 110,
      sortable: true,
      renderCell: (value: string) => (
        <Typography variant="body2" color="text.secondary" title={value || "N/A"} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
          {value || "N/A"}
        </Typography>
      ),
    },
    {
      field: "dateCreated",
      headerName: "Created Date",
      width: 150,
      minWidth: 130,
      sortable: true,
      renderCell: (value: string) => {
        const formattedDate = new Date(value).toLocaleDateString();
        return (
          <Typography variant="body2" color="text.secondary" title={formattedDate} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
            {formattedDate}
          </Typography>
        );
      },
    },
    {
      field: "dateUpdated",
      headerName: "Updated Date",
      width: 150,
      minWidth: 130,
      sortable: true,
      renderCell: (value: string) => {
        const formattedDate = new Date(value).toLocaleDateString();
        return (
          <Typography variant="body2" color="text.secondary" title={formattedDate} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
            {formattedDate}
          </Typography>
        );
      },
    },
  ];

  // Create sort model from current filters with default to dateUpdated
  const sortModel = {
    field: filters.sortBy || "dateUpdated",
    sort: filters.sortOrder || "desc",
  };

  return (
    <Box sx={{ height: "100%", width: "100%" }}>
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Custom Table */}
      <CustomTable
        columns={columns}
        data={organizations || []}
        loading={loading}
        error={error}
        pagination={pagination}
        sortModel={sortModel}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        onSortChange={handleSortChange}
        onView={handleViewOrganization}
        onEdit={handleEditOrganization}
        onDelete={handleDeleteOrganization}
        permissions={orgPermissions}
        pageSizeOptions={PAGE_SIZE_OPTIONS}
        getRowId={(row) => row.uuid}
      />

      <ConfirmDialog
        open={showDeleteDialog}
        onConfirm={handleDeleteConfirm}
        onCancel={() => setShowDeleteDialog(false)}
        title="Delete Organization"
        message="Are you sure you want to delete this organization?"
      />
    </Box>
  );
};

export default OrganizationList;
