import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  IconButton,
  Typography,
  Box,
  CircularProgress,
  Alert,
} from "@mui/material";
import {
  KeyboardArrowUp as SortUpIcon,
  KeyboardArrowDown as SortDownIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";

export interface Column {
  field: string;
  headerName: string;
  width?: number;
  minWidth?: number;
  sortable?: boolean;
  renderCell?: (value: any, row: any) => React.ReactNode;
}

export interface CustomTableProps {
  columns: Column[];
  data: any[];
  loading?: boolean;
  error?: string | null;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
  sortModel?: {
    field: string;
    sort: "asc" | "desc";
  };
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onSortChange: (field: string, sort: "asc" | "desc") => void;
  onView?: (row: any) => void;
  onEdit?: (row: any) => void;
  onDelete?: (row: any) => void;
  permissions?: {
    view?: boolean;
    edit?: boolean;
    delete?: boolean;
  };
  pageSizeOptions?: number[];
  getRowId?: (row: any) => string;
}

const CustomTable: React.FC<CustomTableProps> = ({
  columns,
  data,
  loading = false,
  error = null,
  pagination,
  sortModel,
  onPageChange,
  onPageSizeChange,
  onSortChange,
  onView,
  onEdit,
  onDelete,
  permissions = {},
  pageSizeOptions = [10, 25, 50],
  getRowId = (row) => row.uuid || row.id,
}) => {
  const handleSort = (field: string) => {
    if (!sortModel || sortModel.field !== field) {
      onSortChange(field, "asc");
    } else {
      onSortChange(field, sortModel.sort === "asc" ? "desc" : "asc");
    }
  };

  const renderSortIcon = (field: string) => {
    if (!sortModel || sortModel.field !== field) {
      return null;
    }
    return sortModel.sort === "asc" ? <SortUpIcon /> : <SortDownIcon />;
  };

  const renderHoverSortIcon = (field: string) => {
    if (!sortModel || sortModel.field !== field) {
      return <SortUpIcon sx={{ opacity: 0.3, fontSize: '1rem' }} />;
    }
    return null;
  };

  const shouldShowHoverIcon = (field: string) => {
    return hoveredColumn === field && (!sortModel || sortModel.field !== field);
  };

  const [hoveredColumn, setHoveredColumn] = useState<string | null>(null);

  const renderCell = (column: Column, row: any) => {
    const value = row[column.field];
    
    if (column.renderCell) {
      return column.renderCell(value, row);
    }
    
    return (
      <Typography variant="body2" color="text.secondary">
        {value || "N/A"}
      </Typography>
    );
  };

  const renderActions = (row: any) => {
    return (
      <Box sx={{ display: "flex", gap: 1 }}>
        {onView && permissions.view && (
          <IconButton
            size="small"
            onClick={() => onView(row)}
            color="primary"
            title="View"
          >
            <ViewIcon />
          </IconButton>
        )}
        {onEdit && permissions.edit && (
          <IconButton
            size="small"
            onClick={() => onEdit(row)}
            color="primary"
            title="Edit"
          >
            <EditIcon />
          </IconButton>
        )}
        {onDelete && permissions.delete && (
          <IconButton
            size="small"
            onClick={() => onDelete(row)}
            color="error"
            title="Delete"
          >
            <DeleteIcon />
          </IconButton>
        )}
      </Box>
    );
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ width: "100%", height: "100vh" }}>
      <Paper sx={{ width: "100%", height: "100%", overflow: "hidden" }}>
        <TableContainer sx={{ height: "calc(100vh - 120px)" }}>
          <Table stickyHeader sx={{ tableLayout: "fixed", width: "100%" }}>
            <TableHead>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.field}
                    style={{
                      width: `${column.width}px`,
                      minWidth: `${column.minWidth || column.width || 120}px`,
                      maxWidth: `${column.width}px`,
                      cursor: column.sortable ? "pointer" : "default",
                      userSelect: "none",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                    onClick={() => column.sortable && handleSort(column.field)}
                    onMouseEnter={() => column.sortable && setHoveredColumn(column.field)}
                    onMouseLeave={() => setHoveredColumn(null)}
                    sx={{
                      "&:hover": {
                        backgroundColor: column.sortable ? "rgba(0, 0, 0, 0.04)" : "inherit",
                      },
                    }}
                  >
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {column.headerName}
                      </Typography>
                      {column.sortable && (
                        <Box sx={{ display: "flex", alignItems: "center", minWidth: 20 }}>
                          {renderSortIcon(column.field)}
                          {shouldShowHoverIcon(column.field) && (
                            <SortUpIcon sx={{ opacity: 0.3, fontSize: '1rem' }} />
                          )}
                        </Box>
                      )}
                    </Box>
                  </TableCell>
                ))}
                {(onView || onEdit || onDelete) && (
                  <TableCell 
                    style={{ 
                      width: "160px", 
                      minWidth: "160px", 
                      maxWidth: "120px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      textAlign: "center",
                    }}
                  >
                    Actions
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={columns.length + 1} align="center">
                    <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center", py: 4 }}>
                      <CircularProgress />
                    </Box>
                  </TableCell>
                </TableRow>
              ) : data.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={columns.length + 1} align="center">
                    <Typography variant="body2" color="text.secondary" sx={{ py: 4 }}>
                      No data available
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                data.map((row) => (
                  <TableRow key={getRowId(row)} hover>
                    {columns.map((column) => (
                      <TableCell 
                        key={column.field}
                        style={{
                          width: `${column.width}px`,
                          minWidth: `${column.minWidth || column.width || 120}px`,
                          maxWidth: `${column.width}px`,
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {renderCell(column, row)}
                      </TableCell>
                    ))}
                    {(onView || onEdit || onDelete) && (
                      <TableCell
                        style={{
                          width: "120px",
                          minWidth: "120px",
                          maxWidth: "120px",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {renderActions(row)}
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          rowsPerPageOptions={pageSizeOptions}
          component="div"
          count={pagination.total}
          rowsPerPage={pagination.pageSize}
          page={pagination.page - 1} // Convert to 0-based for MUI
          onPageChange={(_, newPage) => onPageChange(newPage + 1)} // Convert back to 1-based
          onRowsPerPageChange={(event) => onPageSizeChange(parseInt(event.target.value, 10))}
        />
      </Paper>
    </Box>
  );
};

export default CustomTable;
