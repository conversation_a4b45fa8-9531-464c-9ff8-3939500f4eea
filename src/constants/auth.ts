export const AUTH_CONSTANTS = {
  STORAGE_KEYS: {
    PENDING_TOTP_SETUP: "pendingTOTPSetup",
  },
  ROUTES: {
    DASHBOARD: "/dashboard",
    LOGIN: "/login",
    CHANGE_PASSWORD: "/change-password",
  },
  TIMEOUTS: {
    SESSION_WAIT: 2000,
    ERROR_REDIRECT: 3000,
  },
  QR_CODE: {
    SIZE: 200,
    ISSUER: "CO",
  },
  FORM_VALIDATION: {
    MFA_CODE_LENGTH: 6,
    MFA_CODE_PATTERN: "[0-9]*",
    PASSWORD_MIN_LENGTH: 8,
  },
  MESSAGES: {
    TOTP_SETUP_INSTRUCTION:
      "Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)",
    TOTP_MANUAL_ENTRY: "Can't scan? Enter this code manually:",
    REMEMBER_DEVICE: "Remember this device for 30 days",
    TOTP_SETUP_FAILED:
      "TOTP setup failed. You can enable two-factor authentication later in your account settings.",
    TOTP_SETUP_SUCCESS:
      "Please set up your authenticator app by scanning the QR code below.",
    R<PERSON><PERSON>TRATION_PROCESSING: "Completing your registration. Please wait...",
    REGISTRATION_PROCESSING_SUBTITLE: "This may take a few moments...",
    PASSWORD_CHANGE_SUCCESS:
      "Password changed successfully! Please set up two-factor authentication.",
    PASSWORD_CHANGE_FAILED: "Failed to change password. Please try again.",
    PASSWORD_ALREADY_CHANGED: "This password has already been changed. Please use the login page to access your account.",
    MFA_SETUP_SUCCESS:
      "Two-factor authentication setup completed! You are now logged in.",
    MFA_SETUP_FAILED: "Failed to complete MFA setup. Please try again.",
    INVALID_ACCESS_TOKEN:
      "Invalid or missing access token. Please check your email link.",
  },
  PAGE_TITLES: {
    WELCOME: "Welcome to CO-NOMI",
    TOTP_SETUP: "Set up Two-Factor Authentication",
    MFA_TOTP: "Enter your Authenticator App code",
    MFA_SMS: "Enter the SMS code sent to your phone",
    MEMBER_LOGIN: "Member Dashboard Login",
    REGISTRATION_COMPLETING: "Completing your registration...",
    CHANGE_PASSWORD: "Set Your Password",
  },
} as const;
