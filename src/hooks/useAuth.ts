import { useState, useCallback } from "react";
import { cognitoAuthService } from "@/services/cognitoAuth1";
import { getCurrentUser, setUpTOTP } from "aws-amplify/auth";
import { LoginFormData, MFAFormData } from "@/types/auth";
import { useRouter } from "next/navigation";
import { AUTH_CONSTANTS } from "@/constants/auth";
import { syncCognitoTokensToCookies } from "@/utils/auth";

export interface AuthResult {
  status: string;
  nextStep?: any;
}

export const useAuth = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSignIn = useCallback(
    async (loginData: LoginFormData): Promise<AuthResult> => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await cognitoAuthService.signIn({
          username: loginData.username,
          password: loginData.password,
        });

        if (result.status === "SIGNED_IN") {
          // Sync Cognito tokens to cookies after successful sign in
          const syncSuccess = await syncCognitoTokensToCookies(
            loginData.username
          );
          if (syncSuccess) {
            console.log("✅ Tokens synced to cookies after sign in");
          } else {
            console.warn("⚠️ Failed to sync tokens to cookies");
          }

          // Check for pending TOTP setup
          const pendingTOTP = localStorage.getItem(
            AUTH_CONSTANTS.STORAGE_KEYS.PENDING_TOTP_SETUP
          );
          if (pendingTOTP) {
            try {
              const totpData = JSON.parse(pendingTOTP);
              if (totpData.username === loginData.username) {
                localStorage.removeItem(
                  AUTH_CONSTANTS.STORAGE_KEYS.PENDING_TOTP_SETUP
                );
                // Don't redirect here, let the caller handle TOTP setup
                return result;
              }
            } catch (e) {
              console.error("Error parsing pending TOTP data:", e);
              localStorage.removeItem(
                AUTH_CONSTANTS.STORAGE_KEYS.PENDING_TOTP_SETUP
              );
            }
          }
          // Only redirect if no TOTP setup is needed
          router.push(AUTH_CONSTANTS.ROUTES.DASHBOARD);
        }

        return result;
      } catch (err: any) {
        console.error("Sign in error:", err);
        setError(err.message || "Login failed");
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  const handleMFAConfirmation = useCallback(
    async (mfaData: MFAFormData): Promise<void> => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await cognitoAuthService.confirmMFA(
          mfaData.code,
          mfaData.rememberDevice
        );
        console.log("MFA confirmation result:", result);

        const user = await getCurrentUser();
        console.log("Current user:", user);

        // Sync tokens to cookies after successful MFA
        const syncSuccess = await syncCognitoTokensToCookies(user.username);
        if (syncSuccess) {
          console.log("✅ Tokens synced to cookies after MFA confirmation");
          // Ensure cookies are fully propagated before navigation
          await new Promise(resolve => setTimeout(resolve, 200));
          router.push(AUTH_CONSTANTS.ROUTES.DASHBOARD);
        } else {
          console.warn("⚠️ Failed to sync tokens to cookies after MFA");
          setError("Authentication successful but token sync failed. Please refresh if you encounter issues.");
          setTimeout(() => {
            router.push(AUTH_CONSTANTS.ROUTES.DASHBOARD);
          }, 1000);
        }
      } catch (err: any) {
        console.error("MFA confirmation error:", err);
        setError(err.message || "MFA verification failed");
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  const handleTOTPSetup = useCallback(
    async (code: string): Promise<void> => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await cognitoAuthService.confirmMFA(code, false);
        console.log("TOTP setup result:", result);

        // Sync tokens to cookies after successful TOTP setup
        const currentUser = await getCurrentUser();
        const syncSuccess = await syncCognitoTokensToCookies(
          currentUser.username
        );
        if (syncSuccess) {
          console.log("✅ Tokens synced to cookies after TOTP setup");
        } else {
          console.warn("⚠️ Failed to sync tokens to cookies after TOTP setup");
        }

        router.push(AUTH_CONSTANTS.ROUTES.DASHBOARD);
      } catch (err: any) {
        console.error("TOTP setup error:", err);
        setError(err.message || "TOTP setup verification failed");
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  const setupTOTPAfterLogin = useCallback(
    async (username: string): Promise<string> => {
      try {
        console.log("Setting up TOTP after successful login...");

        // Wait for session to be established
        await new Promise((resolve) =>
          setTimeout(resolve, AUTH_CONSTANTS.TIMEOUTS.SESSION_WAIT)
        );

        // Verify valid session
        const session = await getCurrentUser();
        console.log("Current user verified for TOTP setup:", session.username);

        // Set up TOTP
        const totpSetupResult = await setUpTOTP();
        console.log("TOTP setup successful:", totpSetupResult);

        // Generate setup URI
        let setupUri = "";
        try {
          const uri = totpSetupResult.getSetupUri(
            AUTH_CONSTANTS.QR_CODE.ISSUER,
            username
          );
          setupUri = uri.toString();
        } catch (uriError) {
          // Fallback URI generation
          setupUri = `otpauth://totp/${
            AUTH_CONSTANTS.QR_CODE.ISSUER
          }:${encodeURIComponent(username)}?secret=${
            totpSetupResult.sharedSecret
          }&issuer=${AUTH_CONSTANTS.QR_CODE.ISSUER}`;
        }

        setError(AUTH_CONSTANTS.MESSAGES.TOTP_SETUP_SUCCESS);
        return setupUri;
      } catch (error: any) {
        console.error("TOTP Setup Error:", error);
        setError(AUTH_CONSTANTS.MESSAGES.TOTP_SETUP_FAILED);

        // Continue to dashboard even if TOTP setup fails
        setTimeout(() => {
          setError(null);
          router.push(AUTH_CONSTANTS.ROUTES.DASHBOARD);
        }, AUTH_CONSTANTS.TIMEOUTS.ERROR_REDIRECT);

        throw error;
      }
    },
    [router]
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isLoading,
    error,
    handleSignIn,
    handleMFAConfirmation,
    handleTOTPSetup,
    setupTOTPAfterLogin,
    clearError,
  };
};
