import { useEffect, useCallback } from "react";
import {
  syncCognitoTokensToCookies,
  getCognitoCurrentUser,
  getCognitoTokens,
} from "@/utils/auth";

export interface TokenSyncOptions {
  autoSync?: boolean;
  syncInterval?: number; // in milliseconds
  cookieExpireDays?: number;
}

export const useCognitoTokenSync = (options: TokenSyncOptions = {}) => {
  const {
    autoSync = true,
    syncInterval = 30000, // 30 seconds
    cookieExpireDays = 7,
  } = options;

  // Manual sync function
  const syncTokens = useCallback(
    async (username?: string): Promise<boolean> => {
      try {
        const currentUser = username || getCognitoCurrentUser();
        if (!currentUser) {
          // console.log("No current user found for token sync");
          return false;
        }

        const tokens = getCognitoTokens(currentUser);
        if (!tokens.accessToken || !tokens.idToken) {
          // console.log("No valid tokens found for sync");
          return false;
        }

        const success = await syncCognitoTokensToCookies(
          currentUser,
          cookieExpireDays
        );
        if (success) {
          // console.log("✅ Cognito tokens successfully synced to cookies");
        }
        return success;
      } catch (error) {
        // console.error("❌ Error syncing tokens:", error);
        return false;
      }
    },
    [cookieExpireDays]
  );

  // Check if tokens exist in localStorage
  const hasTokensInLocalStorage = useCallback((): boolean => {
    try {
      const currentUser = getCognitoCurrentUser();
      if (!currentUser) return false;

      const tokens = getCognitoTokens(currentUser);
      return !!(tokens.accessToken && tokens.idToken);
    } catch (error) {
      // console.error("Error checking localStorage tokens:", error);
      return false;
    }
  }, []);

  // Auto sync effect
  useEffect(() => {
    if (!autoSync) return;

    // Initial sync
    syncTokens();

    // Set up interval for periodic sync
    const interval = setInterval(() => {
      if (hasTokensInLocalStorage()) {
        syncTokens();
      }
    }, syncInterval);

    return () => clearInterval(interval);
  }, [autoSync, syncInterval, syncTokens, hasTokensInLocalStorage]);

  // Listen for localStorage changes (when Cognito updates tokens)
  useEffect(() => {
    if (!autoSync) return;

    const handleStorageChange = (event: StorageEvent) => {
      // Check if the change is related to Cognito tokens
      if (event.key && event.key.includes("CognitoIdentityServiceProvider")) {
        // console.log("Cognito localStorage change detected:", event.key);
        // Delay sync to ensure all tokens are updated
        setTimeout(() => {
          syncTokens();
        }, 1000);
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [autoSync, syncTokens]);

  // Listen for focus events to sync when user returns to tab
  useEffect(() => {
    if (!autoSync) return;

    const handleFocus = () => {
      if (hasTokensInLocalStorage()) {
        syncTokens();
      }
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [autoSync, syncTokens, hasTokensInLocalStorage]);

  return {
    syncTokens,
    hasTokensInLocalStorage,
  };
};
