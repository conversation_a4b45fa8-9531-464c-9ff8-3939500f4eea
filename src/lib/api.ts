import axios, {
  AxiosInstance,
  InternalAxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";

// API Configuration
const API_BASE_URL =
  process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";
const API_TIMEOUT = 30000; // 30 seconds

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Add auth token if available
    const token = getAuthToken();
    if (token) {
      config.headers.set("Authorization", `Bearer ${token}`);
    }

    // Add request ID for tracking
    config.headers.set("X-Request-ID", generateRequestId());

    // Log request in development
    if (process.env.NODE_ENV === "development") {
      console.log(
        `🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`,
        {
          data: config.data,
          params: config.params,
          headers: config.headers,
        }
      );
    }

    return config;
  },
  (error: AxiosError) => {
    console.error("❌ Request interceptor error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log response in development
    if (process.env.NODE_ENV === "development") {
      console.log(
        `✅ API Response: ${response.config.method?.toUpperCase()} ${
          response.config.url
        }`,
        {
          status: response.status,
          data: response.data,
          headers: response.headers,
        }
      );
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    // Log error in development
    if (process.env.NODE_ENV === "development") {
      console.error(
        `❌ API Error: ${error.config?.method?.toUpperCase()} ${
          error.config?.url
        }`,
        {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        }
      );
    }

    // Handle 401 Unauthorized - token refresh logic
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const newToken = await refreshAuthToken();
        if (newToken) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Token refresh failed, redirect to login
        handleAuthError();
        return Promise.reject(refreshError);
      }
    }

    // Handle other errors
    handleApiError(error);
    return Promise.reject(error);
  }
);

// Utility functions
function getAuthToken(): string | null {
  if (typeof window === "undefined") return null;
  return (
    localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token")
  );
}

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

async function refreshAuthToken(): Promise<string | null> {
  try {
    const refreshToken =
      localStorage.getItem("refresh_token") ||
      sessionStorage.getItem("refresh_token");
    if (!refreshToken) return null;

    const response = await axios.post("/api/auth/refresh", {
      refresh_token: refreshToken,
    });

    const { access_token, refresh_token } = response.data;

    // Store new tokens
    localStorage.setItem("auth_token", access_token);
    if (refresh_token) {
      localStorage.setItem("refresh_token", refresh_token);
    }

    return access_token;
  } catch (error) {
    console.error("Token refresh failed:", error);
    return null;
  }
}

function handleAuthError(): void {
  // Clear stored tokens
  localStorage.removeItem("auth_token");
  localStorage.removeItem("refresh_token");
  sessionStorage.removeItem("auth_token");
  sessionStorage.removeItem("refresh_token");

  // Redirect to login page
  if (typeof window !== "undefined") {
    window.location.href = "/login";
  }
}

function handleApiError(error: AxiosError): void {
  // Global error handling - can be extended with toast notifications
  const errorMessage = getErrorMessage(error);

  // Log error for monitoring
  console.error("API Error:", {
    url: error.config?.url,
    method: error.config?.method,
    status: error.response?.status,
    message: errorMessage,
    timestamp: new Date().toISOString(),
  });

  // You can integrate with error reporting services here
  // Example: Sentry.captureException(error);
}

function getErrorMessage(error: AxiosError): string {
  const responseData = error.response?.data as any;

  if (responseData?.message) {
    return responseData.message;
  }

  if (responseData?.detail) {
    return responseData.detail;
  }

  if (error.message) {
    return error.message;
  }

  return "An unexpected error occurred";
}

// Export the configured axios instance
export default apiClient;

// Export utility functions for external use
export {
  getAuthToken,
  refreshAuthToken,
  handleAuthError,
  handleApiError,
  getErrorMessage,
};
