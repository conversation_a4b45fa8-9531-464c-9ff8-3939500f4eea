import { NextRequest } from "next/server";

interface ApiCallOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  body?: any;
  headers?: Record<string, string>;
}

/**
 * Common function to make API calls to the Python backend
 * @param request - NextRequest object to extract cookies
 * @param endpoint - Backend endpoint path (without base URL)
 * @param options - Additional options like method, body, headers
 * @returns Promise with the response data
 */
export async function callBackendApi(
  request: NextRequest,
  endpoint: string,
  options: ApiCallOptions = {}
): Promise<any> {
  const { method = "GET", body, headers = {} } = options;

  // Get access token from request cookies
  let accessToken = null;

  // Try to get Cognito access token from cookies first
  const cognitoAccessToken = request.cookies.get("cognito_access_token")?.value;
  if (
    cognitoAccessToken &&
    cognitoAccessToken.trim() !== "" &&
    cognitoAccessToken !== "undefined" &&
    cognitoAccessToken !== "null"
  ) {
    accessToken = cognitoAccessToken;
    console.log(`🔑 Using Cognito token: ${accessToken.substring(0, 20)}...`);
  } else {
    // Fallback to legacy token from cookies
    const legacyToken = request.cookies.get("token")?.value;
    if (
      legacyToken &&
      legacyToken.trim() !== "" &&
      legacyToken !== "undefined" &&
      legacyToken !== "null"
    ) {
      accessToken = legacyToken;
    } else {
      // Try access_token as another fallback
      const accessTokenCookie = request.cookies.get("access_token")?.value;
      if (
        accessTokenCookie &&
        accessTokenCookie.trim() !== "" &&
        accessTokenCookie !== "undefined" &&
        accessTokenCookie !== "null"
      ) {
        accessToken = accessTokenCookie;
        console.log(
          `🔑 Using access_token: ${accessToken.substring(0, 20)}...`
        );
      } else {
        console.warn("⚠️ No access token found in cookies");
      }
    }
  }

  // Prepare headers
  const requestHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    ...headers,
  };

  // Add authorization header if token exists
  if (accessToken) {
    requestHeaders.Authorization = `Bearer ${accessToken}`;
  }

  // Get backend URL from environment
  const backendUrl =
    process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

  // Prepare fetch options
  const fetchOptions: RequestInit = {
    method,
    headers: requestHeaders,
  };

  // Add body for non-GET requests
  if (method !== "GET" && body) {
    fetchOptions.body = JSON.stringify(body);
  }

  // // Log the request for debugging
  // console.log(`🌐 API Request: ${method} ${backendUrl}${endpoint}`);
  // console.log(`📋 Headers:`, requestHeaders);
  if (body) {
    console.log(`📦 Body:`, JSON.stringify(body, null, 2));
  }

  // Make fetch call to Python backend
  const response = await fetch(`${backendUrl}${endpoint}`, fetchOptions);

  // Log the response for debugging
  console.log(`📡 Response status: ${response.status} ${response.statusText}`);

  if (!response.ok) {
    let errorMessage = `Backend responded with status: ${response.status}`;
    let errorDetails = null;

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
      errorDetails = errorData;
      console.error("Backend error details:", errorData);
    } catch (parseError) {
      const errorText = await response.text();
      errorMessage = `${errorMessage} - ${errorText}`;
      errorDetails = errorText;
      console.error("Backend error (text):", errorText);
    }

    throw new Error(errorMessage);
  }

  // Handle empty responses (common for DELETE operations)
  const contentType = response.headers.get("content-type");

  // Check if response has content to parse
  if (
    response.status === 204 ||
    !contentType ||
    !contentType.includes("application/json")
  ) {

    return { success: true, status: response.status };
  } else {
    // Parse JSON response
    const data = await response.json();

    return data;
  }
}
