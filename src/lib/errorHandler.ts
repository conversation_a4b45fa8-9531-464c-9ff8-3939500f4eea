import { AxiosError } from 'axios';
import { 
  formatErrorMessage, 
  formatValidationErrors, 
  categorizeError,
  shouldRetry,
  calculateRetryDelay 
} from '@/utils/api';
import { ValidationError } from '@/types/api';

// Toast notification interface
interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Global error handler class
class GlobalErrorHandler {
  private toastCallbacks: ((toast: ToastNotification) => void)[] = [];
  private retryCallbacks: ((error: AxiosError, retry: () => void) => void)[] = [];

  // Register toast notification callback
  registerToastCallback(callback: (toast: ToastNotification) => void) {
    this.toastCallbacks.push(callback);
  }

  // Register retry callback
  registerRetryCallback(callback: (error: AxiosError, retry: () => void) => void) {
    this.retryCallbacks.push(callback);
  }

  // Show toast notification
  showToast(toast: ToastNotification) {
    this.toastCallbacks.forEach(callback => callback(toast));
  }

  // Handle API errors
  handleApiError(error: AxiosError, context?: string) {

    const errorCategory = categorizeError(error);
    const message = formatErrorMessage(error);
    const validationErrors = formatValidationErrors(error);

    // Log error for monitoring
    this.logError(error, context);

    // Don't show error notifications for network errors when API is not available
    // (this is handled by the API service with mock data fallback)
    if (errorCategory === 'network' && !error.response) {
      return;
    }

    // Show appropriate toast notification
    this.showErrorToast(errorCategory, message, validationErrors);

    // Handle specific error types
    switch (errorCategory) {
      case 'auth':
        this.handleAuthError(error);
        break;
      case 'network':
        this.handleNetworkError(error);
        break;
      case 'server':
        this.handleServerError(error);
        break;
      case 'validation':
        this.handleValidationError(error, validationErrors);
        break;
      default:
        this.handleGenericError(error);
    }
  }

  // Handle authentication errors
  private handleAuthError(error: AxiosError) {
    // Clear stored tokens
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      sessionStorage.removeItem('auth_token');
      sessionStorage.removeItem('refresh_token');
    }

    // Redirect to login after a short delay
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }, 2000);
  }

  // Handle network errors
  private handleNetworkError(error: AxiosError) {
    this.showToast({
      id: `network-error-${Date.now()}`,
      type: 'error',
      title: 'Network Error',
      message: 'Please check your internet connection and try again.',
      duration: 5000,
      action: {
        label: 'Retry',
        onClick: () => {
          // Trigger retry logic
          this.triggerRetry(error);
        }
      }
    });
  }

  // Handle server errors
  private handleServerError(error: AxiosError) {
    this.showToast({
      id: `server-error-${Date.now()}`,
      type: 'error',
      title: 'Server Error',
      message: 'Our servers are experiencing issues. Please try again later.',
      duration: 8000,
      action: {
        label: 'Retry',
        onClick: () => {
          this.triggerRetry(error);
        }
      }
    });
  }

  // Handle validation errors
  private handleValidationError(error: AxiosError, validationErrors: ValidationError[]) {
    const errorMessage = validationErrors.length > 0
      ? `Please fix the following errors:\n${validationErrors.map(e => `• ${e.field}: ${e.message}`).join('\n')}`
      : formatErrorMessage(error);

    this.showToast({
      id: `validation-error-${Date.now()}`,
      type: 'error',
      title: 'Validation Error',
      message: errorMessage,
      duration: 6000
    });
  }

  // Handle generic errors
  private handleGenericError(error: AxiosError) {
    this.showToast({
      id: `generic-error-${Date.now()}`,
      type: 'error',
      title: 'Error',
      message: formatErrorMessage(error),
      duration: 5000
    });
  }

  // Show error toast based on category
  private showErrorToast(
    category: 'network' | 'auth' | 'validation' | 'server' | 'unknown',
    message: string,
    validationErrors: ValidationError[]
  ) {
    const toastConfig = {
      network: {
        title: 'Network Error',
        type: 'error' as const,
        duration: 5000
      },
      auth: {
        title: 'Authentication Error',
        type: 'error' as const,
        duration: 3000
      },
      validation: {
        title: 'Validation Error',
        type: 'error' as const,
        duration: 6000
      },
      server: {
        title: 'Server Error',
        type: 'error' as const,
        duration: 8000
      },
      unknown: {
        title: 'Error',
        type: 'error' as const,
        duration: 5000
      }
    };

    const config = toastConfig[category];
    
    this.showToast({
      id: `${category}-error-${Date.now()}`,
      type: config.type,
      title: config.title,
      message: validationErrors.length > 0
        ? `Please fix the following errors:\n${validationErrors.map(e => `• ${e.field}: ${e.message}`).join('\n')}`
        : message,
      duration: config.duration
    });
  }

  // Trigger retry logic
  private triggerRetry(error: AxiosError) {
    this.retryCallbacks.forEach(callback => {
      callback(error, () => {
        // Retry the original request
        if (error.config) {
          // This would need to be implemented based on your retry strategy
          console.log('Retrying request:', error.config.url);
        }
      });
    });
  }

  // Log error for monitoring
  private logError(error: AxiosError, context?: string) {
    const errorLog = {
      timestamp: new Date().toISOString(),
      context,
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      responseData: error.response?.data,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('🚨 API Error:', errorLog);
    }

    // Send to error reporting service (e.g., Sentry)
    // Example: Sentry.captureException(error, { extra: errorLog });
  }

  // Success notification
  showSuccess(message: string, title: string = 'Success') {
    this.showToast({
      id: `success-${Date.now()}`,
      type: 'success',
      title,
      message,
      duration: 3000
    });
  }

  // Warning notification
  showWarning(message: string, title: string = 'Warning') {
    this.showToast({
      id: `warning-${Date.now()}`,
      type: 'warning',
      title,
      message,
      duration: 4000
    });
  }

  // Info notification
  showInfo(message: string, title: string = 'Information') {
    this.showToast({
      id: `info-${Date.now()}`,
      type: 'info',
      title,
      message,
      duration: 4000
    });
  }

  // Retry with exponential backoff
  async retryWithBackoff<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: AxiosError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error as AxiosError;
        
        if (attempt === maxRetries || !shouldRetry(lastError, attempt, maxRetries)) {
          throw lastError;
        }
        
        const delay = calculateRetryDelay(attempt, baseDelay);
        
        // Show retry notification
        this.showToast({
          id: `retry-${Date.now()}`,
          type: 'warning',
          title: 'Retrying...',
          message: `Attempt ${attempt + 1} of ${maxRetries + 1}. Retrying in ${delay / 1000}s...`,
          duration: delay
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }
}

// Create singleton instance
const globalErrorHandler = new GlobalErrorHandler();

// Export the singleton instance
export default globalErrorHandler;

// Export types for external use
export type { ToastNotification }; 