import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { auth0 } from "./lib/auth0";
import jwt from "jsonwebtoken";

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  "/",
  "/login",
  "/forgot-password",
  "/register",
  "/adminRegister",
  "/change-password",
];

// Auth0 routes that should be handled by Auth0 middleware
const AUTH0_ROUTES = [
  "/auth/login",
  "/auth/logout",
  "/auth/callback",
  "/callback",
  "/auth/refresh",
];

// Member-specific routes (require Auth0 authentication)
const MEMBER_ROUTES = ["/member-user"];

// Admin dashboard route
const ADMIN_DASHBOARD = "/dashboard";

// Member dashboard route
const MEMBER_DASHBOARD = "/member-user/dashboard";

/**
 * Check if admin access token is expired
 */
function isAdminTokenExpired(accessToken: string): boolean {
  try {
    if (!accessToken || accessToken.trim() === "") {
      return true;
    }

    const decoded = jwt.decode(accessToken, { complete: true });
    if (!decoded || typeof decoded === "string" || !decoded.payload) {
      return true;
    }

    const payload = decoded.payload as any;
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= payload.exp;
  } catch (error) {
    console.error("Admin token validation error:", error);
    return true;
  }
}

/**
 * Check if admin user is authenticated via Cognito tokens
 */
function isAdminAuthenticated(request: NextRequest): {
  isAuth: boolean;
  needsRefresh: boolean;
} {
  const accessToken = request.cookies.get("cognito_access_token")?.value;
  const idToken = request.cookies.get("cognito_id_token")?.value;
  const refreshToken = request.cookies.get("cognito_refresh_token")?.value;

  // If no access token, admin is not authenticated
  if (!accessToken || !idToken) {
    return { isAuth: false, needsRefresh: false };
  }

  // Check if access token is expired
  const isExpired = isAdminTokenExpired(accessToken);

  if (isExpired) {
    // If expired but has refresh token, needs refresh
    if (refreshToken) {
      return { isAuth: false, needsRefresh: true };
    }
    // If expired and no refresh token, not authenticated
    return { isAuth: false, needsRefresh: false };
  }

  // Token is valid
  return { isAuth: true, needsRefresh: false };
}

/**
 * Clean path by removing trailing slashes
 */
function getCleanPath(pathname: string): string {
  return pathname.replace(/\/+$/, "") || "/";
}

export async function middleware(request: NextRequest) {
  // Skip middleware for static and internal files
  if (
    request.nextUrl.pathname.startsWith("/api/") ||
    request.nextUrl.pathname.startsWith("/_next/") ||
    request.nextUrl.pathname.match(/\.(ico|png|jpg|js|css|svg)$/)
  ) {
    return NextResponse.next();
  }

  console.log("Middleware - Processing path:", request.nextUrl.pathname);

  const pathname = getCleanPath(request.nextUrl.pathname);

  // Check authentication status
  const auth0Session = await auth0.getSession(request);
  const adminAuthStatus = isAdminAuthenticated(request);

  console.log("Middleware - Auth status:", {
    auth0Session: !!auth0Session,
    adminAuth: adminAuthStatus.isAuth,
    adminNeedsRefresh: adminAuthStatus.needsRefresh,
  });

  // Handle Auth0 routes first - let Auth0 middleware handle these
  const isAuth0Route =
    AUTH0_ROUTES.includes(pathname) || pathname.startsWith("/auth/");

  if (isAuth0Route) {
    console.log(
      "Middleware - Auth0 route, delegating to Auth0 middleware:",
      pathname
    );
    const auth0Result = await auth0.middleware(request);
    if (auth0Result) {
      console.log("Middleware - Handled by Auth0 middleware");
      return auth0Result;
    }
    return NextResponse.next();
  }

  // Check if current route is public
  const isPublicRoute = PUBLIC_ROUTES.includes(pathname);

  // Special handling for change-password route
  if (pathname === "/change-password") {
    console.log("Middleware - Change password route accessed");

    // If both admin and member are authenticated, redirect to admin dashboard
    if (adminAuthStatus.isAuth && auth0Session) {
      console.log("Middleware - Authenticated admin and member trying to access change-password, redirecting to admin dashboard");
      return NextResponse.redirect(new URL(ADMIN_DASHBOARD, request.url));
    }

    // If admin is authenticated, redirect to admin dashboard
    if (adminAuthStatus.isAuth) {
      console.log("Middleware - Authenticated admin trying to access change-password, redirecting to admin dashboard");
      return NextResponse.redirect(new URL(ADMIN_DASHBOARD, request.url));
    }

    // If member is authenticated, redirect to member dashboard
    if (auth0Session) {
      console.log("Middleware - Authenticated member trying to access change-password, redirecting to member dashboard");
      return NextResponse.redirect(new URL(MEMBER_DASHBOARD, request.url));
    }

    // If admin needs token refresh, allow access (they're essentially unauthenticated at this point)
    if (adminAuthStatus.needsRefresh) {
      console.log("Middleware - Admin token needs refresh, allowing access to change-password");
      return NextResponse.next();
    }

    // Only unauthenticated users can access change-password
    console.log("Middleware - Unauthenticated user accessing change-password, allowing access");
    return NextResponse.next();
  }

  // Handle public routes
  if (isPublicRoute) {
    console.log("Middleware - Public route, checking authentication status");

    // If both admin and member are authenticated, prioritize admin (or handle based on your business logic)
    if (adminAuthStatus.isAuth && auth0Session) {
      console.log("Middleware - Both admin and member authenticated, redirecting to admin dashboard");
      return NextResponse.redirect(new URL(ADMIN_DASHBOARD, request.url));
    }

    // If admin is authenticated, redirect to admin dashboard
    if (adminAuthStatus.isAuth) {
      console.log("Middleware - Authenticated admin accessing public route, redirecting to admin dashboard");
      return NextResponse.redirect(new URL(ADMIN_DASHBOARD, request.url));
    }

    // If member is authenticated, redirect to member dashboard
    if (auth0Session) {
      console.log("Middleware - Authenticated member accessing public route, redirecting to member dashboard");
      return NextResponse.redirect(new URL(MEMBER_DASHBOARD, request.url));
    }

    // If admin needs token refresh, allow access and let background refresh handle it
    if (adminAuthStatus.needsRefresh) {
      console.log("Middleware - Admin token needs refresh, allowing access for background refresh to handle");
      return NextResponse.next();
    }

    // If not authenticated, allow access to public routes
    console.log("Middleware - Unauthenticated user, allowing access to public route:", pathname);
    return NextResponse.next();
  }

  // Handle member routes
  const isMemberRoute = MEMBER_ROUTES.some((route) =>
    pathname.startsWith(route)
  );

  if (isMemberRoute) {
    console.log("Middleware - Member route, checking Auth0 authentication");

    // If admin is also authenticated, you might want to allow admin access to member routes
    // or redirect to admin dashboard based on your business logic
    if (adminAuthStatus.isAuth) {
      console.log("Middleware - Admin trying to access member route, allowing access (or redirect based on business logic)");
      return NextResponse.next(); // or redirect to admin dashboard
    }

    // If not authenticated with Auth0, redirect to login
    if (!auth0Session) {
      console.log("Middleware - Unauthenticated member accessing protected route, redirecting to login");
      return NextResponse.redirect(new URL("/", request.url));
    }

    // If authenticated, allow access
    console.log("Middleware - Authenticated member, allowing access to:", pathname);
    return NextResponse.next();
  }

  // Handle admin routes (all other routes are considered admin routes)
  console.log("Middleware - Admin route, checking Cognito authentication");

  // If admin needs token refresh, allow access and let background refresh handle it
  if (adminAuthStatus.needsRefresh) {
    console.log("Middleware - Admin token needs refresh, allowing access for background refresh to handle");
    return NextResponse.next();
  }

  // If not authenticated, redirect to login
  if (!adminAuthStatus.isAuth) {
    console.log("Middleware - Unauthenticated admin accessing protected route, redirecting to login");
    console.log("Middleware - Auth details:", {
      hasAccessToken: !!request.cookies.get("cognito_access_token")?.value,
      hasIdToken: !!request.cookies.get("cognito_id_token")?.value,
      hasRefreshToken: !!request.cookies.get("cognito_refresh_token")?.value,
      path: pathname
    });
    return NextResponse.redirect(new URL("/", request.url));
  }

  // If authenticated, allow access
  console.log("Middleware - Authenticated admin, allowing access to:", pathname);
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon\\.ico).*)"],
};