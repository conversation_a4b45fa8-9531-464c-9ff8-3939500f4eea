// Remove imports since we'll use fetch directly

export interface CreateAdminUserRequest {
  email: string;
  username: string;
  password: string;
  roles?: ("super_admin" | "admin" | "moderator")[];
}

export interface CreateAdminUserResponse {
  success: boolean;
  message?: string;
  error?: string;
  details?: string;
  data?: {
    userId: string;
    username: string;
    email: string;
    role: string;
    status: string;
    createdBy: string;
    createdAt: string;
  };
}

// Remove this function since we'll use fetch directly

/**
 * Create a new admin user
 */
export async function createAdminUser(
  userData: CreateAdminUserRequest
): Promise<CreateAdminUserResponse> {
  try {
    const response = await fetch("/api/admin/register", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error:
          data.message || `Failed to create admin user: ${response.status}`,
        details: data.details || "An unexpected error occurred",
      };
    }

    return data;
  } catch (error: any) {
    console.error("Create admin user error:", error);
    return {
      success: false,
      error: error.message || "Failed to create admin user",
      details: "An unexpected error occurred",
    };
  }
}

/**
 * Get list of admin users (placeholder for future implementation)
 */
export async function getAdminUsers(): Promise<any> {
  try {
    const response = await fetch("/api/admin/users");
    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.message || `Failed to get admin users: ${response.status}`
      );
    }

    return data;
  } catch (error: any) {
    console.error("Get admin users error:", error);
    throw new Error(error.message || "Failed to get admin users");
  }
}

/**
 * Fetch a single admin user by UUID
 * @param uuid The UUID of the admin user to fetch
 * @returns AdminUser object
 */
export async function fetchAdminUserByUuid(uuid: string): Promise<any> {
  try {
    const response = await fetch(`/api/admin/user/${uuid}`);
    const responseData = await response.json();

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error("Admin user not found");
      }
      if (response.status === 403) {
        throw new Error("You don't have permission to access this user");
      }
      throw new Error(
        responseData.message || `HTTP error! status: ${response.status}`
      );
    }

    // Check if the response has the expected structure
    if (!responseData.success || !responseData.admin) {
      throw new Error(responseData.message || "Invalid response format");
    }

    const adminData = responseData.admin;
    console.log("Raw API response:", responseData);
    console.log("Admin data:", adminData);

    // Transform the response to match AdminUser interface
    return {
      uuid: adminData.uuid,
      username: adminData.username || "",
      email: adminData.email || "",
      firstName: adminData.firstName || null,
      lastName: adminData.lastName || null,
      phone: adminData.phone || null,
      countrycode: adminData.countrycode || null,
      isactive: adminData.isactive !== undefined ? adminData.isactive : true,
      istemppassword: adminData.istemppassword || false,
      emailVerified: adminData.emailVerified || false,
      roles: Array.isArray(adminData.roles)
        ? adminData.roles
        : [adminData.roles],
      createdBy: adminData.createdBy || "",
      permissions: adminData.permissions || [],
      // Additional fields from API response
      cognitoid: adminData.cognitoid || null,
      updatedBy: adminData.updatedBy || null,
      lastlogin: adminData.lastlogin || null,
      dateCreated: adminData.dateCreated || null,
      dateUpdated: adminData.dateUpdated || null,
    };
  } catch (error: any) {
    console.error("Fetch admin user by UUID error:", error);
    throw new Error(error.message || "Failed to fetch admin user");
  }
}

/**
 * Update an admin user using the /api/admin/{uuid} endpoint
 */
export async function updateAdminUser(
  uuid: string,
  userData: {
    password?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
    countrycode?: string;
    isactive?: boolean;
    istemppassword?: boolean;
    roles?: string[];
  }
): Promise<any> {
  try {
    // Only include the allowed fields in the request body
    const requestBody = {
      ...(userData.password && { password: userData.password }),
      ...(userData.email && { email: userData.email }),
      ...(userData.firstName && { firstName: userData.firstName }),
      ...(userData.lastName && { lastName: userData.lastName }),
      ...(userData.phone && { phone: userData.phone }),
      ...(userData.countrycode && { countrycode: userData.countrycode }),
      ...(userData.isactive !== undefined && { isactive: userData.isactive }),
      ...(userData.istemppassword !== undefined && {
        istemppassword: userData.istemppassword,
      }),
      ...(userData.roles && { roles: userData.roles }),
    };

    const response = await fetch(`/api/admin/user/${uuid}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || "Failed to update admin user",
      };
    }

    // Return the API response directly as it matches the expected structure
    return data;
  } catch (error: any) {
    console.error("Update admin user error:", error);
    return {
      success: false,
      error: error.message || "Failed to update admin user",
    };
  }
}

/**
 * Delete an admin user using the /api/admin/{uuid} endpoint
 */
export async function deleteAdminUser(uuid: string): Promise<any> {
  try {
    const response = await fetch(`/api/admin/user/${uuid}`, {
      method: "DELETE",
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || "Failed to delete admin user",
      };
    }

    return {
      success: true,
      data: data,
      message: "Admin user deleted successfully",
    };
  } catch (error: any) {
    console.error("Delete admin user error:", error);
    return {
      success: false,
      error: error.message || "Failed to delete admin user",
    };
  }
}

/**
 * Fetch the current admin user
 */
export async function fetchCurrentAdminUser(): Promise<any> {
  try {
    const response = await fetch("/api/admin/get-admin-user");
    const data = await response.json();
    return data;
  } catch (error: any) {
    console.error("Fetch current admin user error:", error);
    throw new Error(error.message || "Failed to fetch current admin user");
  }
}
