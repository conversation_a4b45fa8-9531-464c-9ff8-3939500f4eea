// Remove apiClient import since we'll use fetch directly
import {
  AdminUserSearchParams,
  AdminUserStats,
  BulkActionRequest,
} from "@/types/adminUser";

interface AdminListResponse {
  status_code: number;
  success: boolean;
  message: string;
  admins: Admin[];
  pagination: Pagination;
}

interface Admin {
  uuid: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  countryCode: string;
  isActive: boolean;
  isTempPassword: boolean;
  emailVerified: boolean;
  roles: string[];
  cognitoId: string;
  createdBy: CreatedBy;
  updatedBy: string;
  lastLogin?: string;
  dateCreated: string;
  dateUpdated?: string;
}

interface CreatedBy {
  uuid: string;
  username: string;
}

interface Pagination {
  totalCount: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

class AdminUsersService {
  // Get admin users with search, filtering, and pagination
  async getAdminUsers(params: AdminUserSearchParams) {
    const queryParams = new URLSearchParams();
    queryParams.append("page", params.page.toString());
    queryParams.append("pageSize", params.pageSize.toString());
    if (params.sortBy) queryParams.append("sortBy", params.sortBy);
    if (params.sortOrder) queryParams.append("sortOrder", params.sortOrder);

    // Add filters
    if (params.filters?.search) {
      queryParams.append("search", params.filters.search);
    }
    if (params.filters?.role && params.filters.role !== "all") {
      queryParams.append("role", params.filters.role);
    }

    const response = await fetch(
      `/api/admin/admin-list?${queryParams.toString()}`
    );
    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.message || `Failed to fetch admin users: ${response.status}`
      );
    }

    return {
      adminUsers: data.admins || [],
      total: data.pagination?.totalCount || 0,
      page: data.pagination?.currentPage || params.page,
      pageSize: data.pagination?.pageSize || params.pageSize,
      totalPages: data.pagination?.totalPages || 0,
      pagination: {
        totalCount: data.pagination?.totalCount || 0,
        currentPage: data.pagination?.currentPage || params.page,
        pageSize: data.pagination?.pageSize || params.pageSize,
        totalPages: data.pagination?.totalPages || 0,
        hasNext: data.pagination?.hasNext || false,
        hasPrevious: data.pagination?.hasPrevious || false,
      },
    };
  }

  // Get admin user by ID
  async getAdminUser(id: string): Promise<Admin | null> {
    return null;
  }

  // Delete admin user
  async deleteAdminUser(id: string): Promise<void> {
    return;
  }

  // Bulk actions
  async bulkAction(request: BulkActionRequest): Promise<void> {
    return;
  }

  // Get admin user stats
  async getAdminUserStats(): Promise<AdminUserStats> {
    return {
      total: 0,
      active: 0,
      inactive: 0,
      pending: 0,
      superAdmins: 0,
      admins: 0,
      moderators: 0,
      newThisMonth: 0,
    };
  }

  // Get departments
  async getDepartments(): Promise<string[]> {
    return [];
  }

  // Check if user can manage another user
  canManageUser(currentUserRole: string, targetUserRole: string): boolean {
    // This is a placeholder. In a real application, you would check permissions
    // or roles to determine if the current user can manage the target user.
    // For now, we'll assume a simple role-based check.
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1,
    };

    return (
      roleHierarchy[currentUserRole as keyof typeof roleHierarchy] >
      roleHierarchy[targetUserRole as keyof typeof roleHierarchy]
    );
  }
}

export const adminUsersService = new AdminUsersService();
