import amplifyConfig from "@/config/cognito";
import {
  ChangePasswordWithTokenFormData,
  ForgotPasswordFormData,
  LoginFormData,
  MFASetupFormData,
  RegisterFormData,
  ResetPasswordFormData,
  TOTPSetupResponse,
} from "@/types/auth";
import {
  confirmSignIn,
  confirmSignUp,
  fetchAuthSession,
  getCurrentUser,
  rememberDevice,
  resendSignUpCode,
  signIn,
  signOut,
  signUp,
  type AuthUser,
} from "aws-amplify/auth";

export const authApiService = {
  async login(data: LoginFormData): Promise<any> {
    // Cognito signIn
    return signIn({ username: data.username, password: data.password });
  },

  async logout(): Promise<void> {
    await signOut();
  },

  async register(data: RegisterFormData): Promise<any> {
    return signUp({
      username: data.username,
      password: data.password,
      options: { userAttributes: { email: data.email } },
    });
  },

  async confirmSignUp(username: string, code: string): Promise<any> {
    // For admin-created users, we need to provide the ClientId explicitly
    const userPoolClientId = amplifyConfig.Auth.Cognito.userPoolClientId;
    if (!userPoolClientId) {
      throw new Error("AWS Client ID not configured");
    }

    return confirmSignUp({
      username,
      confirmationCode: code,
      options: {
        clientId: userPoolClientId,
      },
    });
  },

  async resendSignUpCode(username: string): Promise<any> {
    return resendSignUpCode({ username });
  },

  async confirmMFA(_username: string, code: string): Promise<any> {
    return confirmSignIn({ challengeResponse: code });
  },

  async setupTOTP(code: string): Promise<any> {
    return confirmSignIn({ challengeResponse: code });
  },

  async rememberDevice(): Promise<any> {
    return rememberDevice();
  },

  async refresh(): Promise<any> {
    return fetchAuthSession({ forceRefresh: true });
  },

  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      return await getCurrentUser();
    } catch {
      return null;
    }
  },

  // Forgot Password API methods - using custom backend endpoints
  async initiateForgotPassword(data: ForgotPasswordFormData): Promise<any> {
    const response = await fetch("/api/admin/forgot-password", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        username: data.email,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to initiate forgot password");
    }

    return result;
  },

  async confirmForgotPassword(data: ResetPasswordFormData): Promise<any> {
    const response = await fetch("/api/admin/confirm-forgot-password", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        username: data.email,
        confirmationCode: data.code,
        newPassword: data.newPassword,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to confirm forgot password");
    }

    return result;
  },

  // Change Password with Access Token (for admin-created users)
  async changePasswordWithToken(
    data: ChangePasswordWithTokenFormData
  ): Promise<any> {
    const response = await fetch("/api/admin/change-password", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        accessToken: data.accessToken,
        newPassword: data.newPassword,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to change password");
    }

    return result;
  },

  // Get TOTP setup data
  async getTOTPSetupData(): Promise<TOTPSetupResponse> {
    const response = await fetch("/api/admin/totp-setup", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(
        result.detail?.message || "Failed to get TOTP setup data"
      );
    }

    return result;
  },

  // Complete TOTP setup
  async completeTOTPSetup(data: MFASetupFormData): Promise<any> {
    const response = await fetch("/api/admin/totp-setup", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        code: data.code,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(
        result.detail?.message || "Failed to complete TOTP setup"
      );
    }

    return result;
  },
};
