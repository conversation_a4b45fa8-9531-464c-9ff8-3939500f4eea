// Industry Service
import { Industry } from "@/types/industry";

export interface CreateIndustryData {
  name: string;
  description?: string;
}

export interface UpdateIndustryData {
  name?: string;
  description?: string;
}

export interface IndustriesResponse {
  industries: Industry[];
  total: number;
  page: number;
  size: number;
}

export const industryService = {
  // Get all industries with pagination and search
  getIndustries: async (params?: {
    page?: number;
    size?: number;
    search?: string;
  }): Promise<IndustriesResponse> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.size) queryParams.append("size", params.size.toString());
    if (params?.search) queryParams.append("search", params.search);

    const response = await fetch(`/api/industries?${queryParams}`);
    const data = await response.json();
    return data;
  },

  // Get industry by ID
  getIndustry: async (id: string): Promise<Industry> => {
    const response = await fetch(`/api/industries/${id}`);
    const data = await response.json();
    return data;
  },

  // Get industry by UUID
  getIndustryByUuid: async (uuid: string): Promise<Industry> => {
    const response = await fetch(`/api/industries/uuid/${uuid}`);
    const data = await response.json();
    return data;
  },

  // Create new industry
  createIndustry: async (data: CreateIndustryData): Promise<Industry> => {
    const response = await fetch("/api/industries", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    return responseData;
  },

  // Update industry
  updateIndustry: async (
    id: string,
    data: UpdateIndustryData
  ): Promise<Industry> => {
    const response = await fetch(`/api/industries/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    return responseData;
  },

  // Delete industry
  deleteIndustry: async (id: string): Promise<void> => {
    await fetch(`/api/industries/${id}`, {
      method: "DELETE",
    });
  },
};
