// Remove apiClient import since we'll use fetch directly
import {
  LogFilterParams,
  LogListResponse,
  ExportRequest,
  ExportResponse,
  ActionsResponse,
  ExportFieldsResponse,
} from "@/types/log";

class LogApiService {
  // Get logs with filtering and pagination
  async getLogs(params?: LogFilterParams): Promise<LogListResponse> {
    const queryParams = new URLSearchParams();

    if (params) {
      if (params.start_timestamp)
        queryParams.append("start_timestamp", params.start_timestamp);
      if (params.end_timestamp)
        queryParams.append("end_timestamp", params.end_timestamp);
      if (params.purpose) queryParams.append("purpose", params.purpose);
      if (params.action) queryParams.append("action", params.action);
      if (params.action_exclude) {
        if (Array.isArray(params.action_exclude)) {
          params.action_exclude.forEach((action) => {
            queryParams.append("action_exclude", action);
          });
        } else {
          queryParams.append("action_exclude", params.action_exclude);
        }
      }
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.page_size) {
        queryParams.append("page_size", params.page_size.toString());
      }
      if (params.limit) {
        // Ensure limit doesn't exceed 1000
        const limit = Math.min(params.limit, 1000);
        queryParams.append("limit", limit.toString());
      }
      queryParams.append("sort_by", "timestamp");
      if (params.sort_order)
        queryParams.append("sort_order", params.sort_order);
    } else {
      queryParams.append("sort_by", "timestamp");
      queryParams.append("sort_order", "desc");
    }

    const url = `/api/logs/?${queryParams.toString()}`;
    console.log("Fetching logs with URL:", url);

    const response = await fetch(url);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.message || `Failed to fetch logs: ${response.status}`
      );
    }

    return data;
  }

  // Get my logs (for member users)
  async getMyLogs(params?: LogFilterParams): Promise<LogListResponse> {
    const queryParams = new URLSearchParams();

    if (params) {
      if (params.start_timestamp)
        queryParams.append("start_timestamp", params.start_timestamp);
      if (params.end_timestamp)
        queryParams.append("end_timestamp", params.end_timestamp);
      if (params.purpose) queryParams.append("purpose", params.purpose);
      if (params.action) queryParams.append("action", params.action);
      if (params.action_exclude) {
        if (Array.isArray(params.action_exclude)) {
          params.action_exclude.forEach((action) => {
            queryParams.append("action_exclude", action);
          });
        } else {
          queryParams.append("action_exclude", params.action_exclude);
        }
      }
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.page_size) {
        queryParams.append("page_size", params.page_size.toString());
      }
      if (params.limit) {
        // Ensure limit doesn't exceed 1000
        const limit = Math.min(params.limit, 1000);
        queryParams.append("limit", limit.toString());
      }
      queryParams.append("sort_by", "timestamp");
      if (params.sort_order)
        queryParams.append("sort_order", params.sort_order);
    } else {
      queryParams.append("sort_by", "timestamp");
      queryParams.append("sort_order", "desc");
    }

    const url = `/api/logs/my-logs?${queryParams.toString()}`;
    console.log("Fetching my logs with URL:", url);

    const response = await fetch(url);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.message || `Failed to fetch my logs: ${response.status}`
      );
    }

    return data;
  }

  // Export logs to CSV
  async exportLogs(exportRequest: ExportRequest): Promise<ExportResponse> {
    const response = await fetch("/api/logs/export", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(exportRequest),
    });
    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.message || `Failed to export logs: ${response.status}`
      );
    }

    return data;
  }

  // Get available actions
  async getActions(): Promise<ActionsResponse> {
    const response = await fetch("/api/logs/actions");
    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.message || `Failed to fetch actions: ${response.status}`
      );
    }

    return data;
  }

  // Get available export fields
  async getExportFields(): Promise<ExportFieldsResponse> {
    const response = await fetch("/api/logs/export-fields");
    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.message || `Failed to fetch export fields: ${response.status}`
      );
    }

    return data;
  }

  // Helper method to download CSV file
  downloadCSV(csvContent: string, filename: string): void {
    // Decode base64 content
    const decodedContent = atob(csvContent);
    const blob = new Blob([decodedContent], {
      type: "text/csv;charset=utf-8;",
    });

    // Create download link
    const link = document.createElement("a");
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  }

  // Helper method to format timestamp for API
  formatTimestamp(date: Date): string {
    return date.toISOString();
  }

  // Helper method to parse filters_applied JSON safely
  parseFiltersApplied(filtersApplied: any): string {
    if (!filtersApplied) return "None";
    if (typeof filtersApplied === "string") return filtersApplied;
    try {
      return JSON.stringify(filtersApplied, null, 2);
    } catch {
      return String(filtersApplied);
    }
  }

  // Helper method to parse selected_fields array safely
  parseSelectedFields(selectedFields: any): string {
    if (!selectedFields) return "None";
    if (Array.isArray(selectedFields)) return selectedFields.join(", ");
    return String(selectedFields);
  }
}

export const logApiService = new LogApiService();
