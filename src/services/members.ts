import {
  Member,
  MemberWithRelations,
  MemberSearchFilters,
  MemberBulkAction,
  VerificationRequest,
  MemberWithOrganizationsResponse,
  MemberExportRequest,
  MemberExportResponse,
  ExportJobStatus,
  ExportJobsList,
} from "@/types/member";
import {
  CreateMemberData,
  UpdateMemberData,
  type OrganizationData,
  type NewOrganizationData,
} from "@/lib/validations/member";

export interface OrganizationResponse {
  status_code: number;
  success: boolean;
  message: string;
  organizations: Organization[];
  pagination: Pagination;
}

export interface Organization {
  name: string;
  address1?: string;
  address2?: string;
  city: string;
  state?: string;
  zip?: string;
  phone?: string;
  annualRevenue?: string;
  industry: string;
  yearFounded?: string;
  businessProfileElementId?: number;
  email?: string;
  companySize?: string;
  uuid: string;
  createdBy: string;
  updatedBy: string;
  dateCreated: string;
  dateUpdated: string;
}

export interface Pagination {
  totalCount: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Enhanced members service using apiClient
export const membersService = {
  // Get members with pagination and filters - UPDATED for new API
  async getMembers(params: {
    page: number;
    pageSize: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    filters: MemberSearchFilters;
  }) {
    const queryParams = new URLSearchParams();
    queryParams.append("page", params.page.toString());
    queryParams.append("pageSize", params.pageSize.toString());
    if (params.sortBy) queryParams.append("sortBy", params.sortBy);
    if (params.sortOrder) queryParams.append("sortOrder", params.sortOrder);

    // Add filters for new API
    if (params.filters.search)
      queryParams.append("search", params.filters.search);
    if (params.filters.id)
      queryParams.append("id", params.filters.id.toString());
    if (params.filters.uuid) queryParams.append("uuid", params.filters.uuid);
    if (params.filters.firstName)
      queryParams.append("firstName", params.filters.firstName);
    if (params.filters.lastName)
      queryParams.append("lastName", params.filters.lastName);
    if (params.filters.email) queryParams.append("email", params.filters.email);
    if (params.filters.membershipTier)
      queryParams.append("membershipTier", params.filters.membershipTier);
    if (params.filters.organizationName)
      queryParams.append("organizationName", params.filters.organizationName);
    if (params.filters.organizationCity)
      queryParams.append("organizationCity", params.filters.organizationCity);
    if (params.filters.organizationState)
      queryParams.append("organizationState", params.filters.organizationState);
    if (params.filters.organizationZip)
      queryParams.append("organizationZip", params.filters.organizationZip);
    if (params.filters.companySize)
      queryParams.append("companySize", params.filters.companySize);
    if (params.filters.industry)
      queryParams.append("industry", params.filters.industry);

    // Add date filters
    if (params.filters.dateCreatedFrom)
      queryParams.append("dateCreatedFrom", params.filters.dateCreatedFrom);
    if (params.filters.dateCreatedTo)
      queryParams.append("dateCreatedTo", params.filters.dateCreatedTo);

    // Add hasOrganizations filter
    if (params.filters.hasOrganizations !== undefined)
      queryParams.append(
        "hasOrganizations",
        params.filters.hasOrganizations.toString()
      );

    // Add nameFilter
    if (params.filters.nameFilter && params.filters.nameFilter !== "all")
      queryParams.append("nameFilter", params.filters.nameFilter);

    const response = await fetch(
      `/api/admin/bulk/with-organizations?${queryParams.toString()}`
    );
    const data = await response.json();

    // Return response with proper pagination structure
    return {
      members: data.members || [],
      pagination: {
        totalCount: data.pagination?.totalCount || 0,
        currentPage: data.pagination?.currentPage || params.page,
        pageSize: data.pagination?.pageSize || params.pageSize,
        totalPages: data.pagination?.totalPages || 0,
        hasNext: data.pagination?.hasNext || false,
        hasPrevious: data.pagination?.hasPrevious || false,
      },
    };
  },

  // Get member by ID with all relations
  async getMember(id: string) {
    const response = await fetch(`/api/members/uuid/${id}`);
    const data = await response.json();

    // Extract the member data from the response structure
    return data.member;
  },

  // Create new member
  async createMember(memberData: CreateMemberData) {
    const response = await fetch("/api/admin/create-member", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(memberData),
    });
    const data = await response.json();
    return data;
  },

  // Update member
  async updateMember(id: string, memberData: UpdateMemberData) {
    const response = await fetch(`/api/admin/update-member/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(memberData),
    });
    const data = await response.json();
    return data;
  },

  // Delete member
  async deleteMember(id: string) {
    const response = await fetch(`/api/members/${id}`, {
      method: "DELETE",
    });
    const data = await response.json();
    return data;
  },

  // Bulk actions
  async bulkAction(params: MemberBulkAction) {
    const response = await fetch("/api/members/bulk", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(params),
    });
    const data = await response.json();
    return data;
  },

  // Bulk delete members
  async bulkDeleteMembers(memberUuids: string[]) {
    const response = await fetch("/api/admin/bulk/delete", {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ memberUuids }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message ||
          `Bulk delete failed with status: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  },

  // Verification management
  async verifyMember(request: VerificationRequest) {
    const response = await fetch(`/api/members/${request.memberId}/verify`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });
    const data = await response.json();
    return data;
  },

  // email verification
  async verifyemail(memberId: string) {
    const response = await fetch(`/api/members/${memberId}/verify-email`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({}),
    });
    const data = await response.json();
    return data;
  },

  // Status management
  async updateMemberStatus(memberId: string, status: string) {
    const response = await fetch(`/api/members/${memberId}/status`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ status }),
    });
    const data = await response.json();
    return data;
  },

  // Get organizations for autocomplete
  async getOrganizations(search?: string | null) {
    const response = await fetch(
      search
        ? `/api/organizations?name=${search}&page=1&pageSize=25`
        : "/api/organizations"
    );
    const data = await response.json();
    return data;
  },

  // Create new organization
  async createOrganization(orgData: NewOrganizationData) {
    const response = await fetch("/api/organizations", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(orgData),
    });
    const data = await response.json();
    return data;
  },

  // NEW: Export members to CSV (asynchronous)
  async exportMembers(
    exportRequest: MemberExportRequest
  ): Promise<MemberExportResponse> {
    const response = await fetch("/api/admin/members/export", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(exportRequest),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Export API error:", errorData);

      // If we have detailed error information, use it
      if (errorData.details) {
        throw new Error(
          errorData.details.message ||
            errorData.details.error ||
            errorData.message
        );
      }

      throw new Error(
        errorData.message || `Export failed with status: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  },

  // NEW: Check export job status
  async checkExportJobStatus(jobId: string): Promise<ExportJobStatus> {
    const response = await fetch(`/api/admin/members/export/status/${jobId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Export status API error:", errorData);
      throw new Error(
        errorData.message || `Failed to check export status: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  },

  // NEW: List export jobs
  async listExportJobs(
    page: number = 1,
    pageSize: number = 10,
    status?: string
  ): Promise<ExportJobsList> {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('page_size', pageSize.toString());
    if (status) {
      params.append('status', status);
    }

    const response = await fetch(`/api/admin/members/export/jobs?${params.toString()}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Export jobs listing API error:", errorData);
      throw new Error(
        errorData.message || `Failed to list export jobs: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  },

  // NEW: Delete export job
  async deleteExportJob(jobId: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`/api/admin/members/export/jobs/${jobId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Export job deletion API error:", errorData);
      throw new Error(
        errorData.message || `Failed to delete export job: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  },

  // NEW: Get member metrics
  async getMemberMetrics(request: { startDate?: string; endDate?: string }) {
    const response = await fetch("/api/members/metrics", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Member metrics API error:", errorData);
      throw new Error(
        errorData.message ||
          `Failed to fetch member metrics: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  },

  // NEW: Get comparison member metrics
  async getComparisonMetrics(request: { startDate?: string; endDate?: string }) {
    const response = await fetch("/api/members/metrics", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Comparison metrics API error:", errorData);
      throw new Error(
        errorData.message ||
          `Failed to fetch comparison metrics: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  },

  // NEW: Get authentication metrics
  async getAuthenticationMetrics() {
    const response = await fetch("/api/members/authentication-metrics", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Authentication metrics API error:", errorData);
      throw new Error(
        errorData.message ||
          `Failed to fetch authentication metrics: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  },
};
