import { IModules, ModuleType } from "@/store/module/redux";
import { CreateAdminUserResponse } from "@/types/adminUser";

export const ModuleApi = {
  async fetch(): Promise<{ modules: ModuleType[] } & CreateAdminUserResponse> {
    try {
      const response = await fetch("/api/rbac/modules");
      const data = await response.json();
      return {
        modules: data.modules,
        ...data,
      };
    } catch (error: any) {
      console.error("Module API fetch error:", error);
      throw error;
    }
  },
};
