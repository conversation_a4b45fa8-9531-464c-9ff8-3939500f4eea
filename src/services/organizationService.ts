// Organization Service - Only active APIs
import {
  Organization,
  CreateOrganizationData,
  MemberRelation,
  PaginationParams,
  OrganizationsResponse,
  OrganizationResponse,
  ApiResponse,
  OrganizationApiResponse,
  OrganizationsApiResponse,
  MemberOrganizationsApiResponse,
  MemberRelationApiResponse,
} from "../types/organization";

export const organizationService = {
  // Core CRUD Operations - Only active ones
  createOrganization: async (
    data: CreateOrganizationData
  ): Promise<Organization> => {
    const response = await fetch("/api/organizations", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    return responseData;
  },

  getOrganizations: async (params?: any): Promise<OrganizationsResponse> => {
    const queryParams = new URLSearchParams();

    // Add pagination parameters
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    // Add sorting parameters
    if (params?.sortBy) queryParams.append("sortBy", params.sortBy);
    if (params?.sortOrder) queryParams.append("sortOrder", params.sortOrder);

    // Add filter parameters
    if (params?.name) queryParams.append("name", params.name);
    if (params?.email) queryParams.append("email", params.email);
    if (params?.phone) queryParams.append("phone", params.phone);
    if (params?.industry) queryParams.append("industry", params.industry);
    if (params?.city) queryParams.append("city", params.city);
    if (params?.state) queryParams.append("state", params.state);
    if (params?.zip) queryParams.append("zip", params.zip);
    if (params?.country) queryParams.append("country", params.country);
    if (params?.companySize)
      queryParams.append("companySize", params.companySize);
    if (params?.yearFounded)
      queryParams.append("yearFounded", params.yearFounded);
    if (params?.annualRevenue)
      queryParams.append("annualRevenue", params.annualRevenue);
    if (params?.website) queryParams.append("website", params.website);
    if (params?.address1) queryParams.append("address1", params.address1);
    if (params?.address2) queryParams.append("address2", params.address2);
    if (params?.hasMembers !== undefined)
      queryParams.append("hasMembers", params.hasMembers.toString());

    // Add date range filters
    if (params?.dateCreatedFrom) queryParams.append("dateCreatedFrom", params.dateCreatedFrom);
    if (params?.dateCreatedTo) queryParams.append("dateCreatedTo", params.dateCreatedTo);

    // Add range filters
    if (params?.annualRevenueMin) queryParams.append("annualRevenueMin", params.annualRevenueMin);
    if (params?.annualRevenueMax) queryParams.append("annualRevenueMax", params.annualRevenueMax);
    if (params?.yearFoundedMin) queryParams.append("yearFoundedMin", params.yearFoundedMin);
    if (params?.yearFoundedMax) queryParams.append("yearFoundedMax", params.yearFoundedMax);

    // Add exact match filter
    if (params?.memberCount !== undefined) queryParams.append("memberCount", params.memberCount.toString());

    const response = await fetch(`/api/organizations?${queryParams}`);
    const data = await response.json();
    return {
      organizations: data.organizations,
      pagination: {
        page: data.pagination.currentPage,
        pageSize: data.pagination.pageSize,
        total: data.pagination.totalCount,
      },
    };
  },

  getOrganization: async (uuid: string): Promise<Organization> => {
    const response = await fetch(`/api/organizations/${uuid}`);
    const data = await response.json();
    return data;
  },

  updateOrganization: async (
    uuid: string,
    data: CreateOrganizationData
  ): Promise<Organization> => {
    const response = await fetch(`/api/organizations/${uuid}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    return responseData;
  },

  deleteOrganization: async (uuid: string): Promise<void> => {
    await fetch(`/api/organizations/${uuid}`, {
      method: "DELETE",
    });
  },

  getOrganizationsByMember: async (
    memberId: string
  ): Promise<Organization[]> => {
    const response = await fetch(
      `/api/organizations/member/${memberId}/organizations`
    );
    const data = await response.json();
    return data.organizations;
  },

  getOrganizationMembers: async (
    uuid: string,
    params?: { perPage?: number; pageSize?: number }
  ): Promise<{ members: any[]; pagination: any }> => {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await fetch(
      `/api/organizations/${uuid}/members?${queryParams}`
    );
    const data = await response.json();

    return {
      members: data.members,
      pagination: data.pagination,
    };
  },

  // Member Relations Operations - Only active ones
  createMemberRelation: async (data: {
    memberUuid: string;
    organizationUuid: string;
  }): Promise<MemberRelation> => {
    const response = await fetch("/api/organizations/relations", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    return responseData.relation;
  },
};

// Assign an organization to a member
export async function assignOrganizationToMember(
  memberUuid: string,
  organizationUuid: string
) {
  const response = await fetch("/api/organizations/relations", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      memberUuid,
      organizationUuid,
    }),
  });
  const data = await response.json();
  return data;
}

// Get all organizations related to a member
export async function getOrganizationsForMember(memberUuid: string) {
  const response = await fetch(
    `/api/organizations/member/${memberUuid}/organizations`
  );
  const data = await response.json();
  return data.organizations;
}

// Remove an organization from a member
export async function removeOrganizationFromMember(
  memberUuid: string,
  organizationUuid: string
) {
  const response = await fetch("/api/organizations/relations/", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ memberUuid, organizationUuid }),
  });
  const data = await response.json();
  return data;
}
