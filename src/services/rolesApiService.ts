import {
  Role,
  CreateRoleData,
  UpdateRoleData,
  AllRolesPermissionsResponse,
} from "@/types/role";

export const rolesApiService = {
  async getRoles(): Promise<Role[]> {
    const response = await fetch("/api/rbac/roles");
    const data = await response.json();
    return data;
  },

  async upsertRole(data: CreateRoleData | UpdateRoleData): Promise<Role> {
    const response = await fetch("/api/rbac/roles/upsert-with-permissions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    return responseData;
  },

  async deleteRole(slug: string): Promise<void> {
    await fetch(`/api/rbac/roles/${slug}`, {
      method: "DELETE",
    });
  },

  async bulkDeleteRoles(roleSlugs: string[]): Promise<void> {
    await fetch("/api/rbac/roles/bulk-delete", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(roleSlugs),
    });
  },

  // Fetch all roles and their permissions
  async getAllRolesPermissions(): Promise<AllRolesPermissionsResponse> {
    const response = await fetch("/api/rbac/permissions");
    const data = await response.json();
    return data;
  },
};
