import { getCognitoTokensFromCookies } from "@/utils/auth";
import jwt from "jsonwebtoken";

export interface UserTokenResponse {
  accessToken: string;
  refreshToken: string;
  idToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface UserProfileResponse {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  permissions: string[];
  mfaEnabled: boolean;
  emailVerified: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

/**
 * Get the current user's tokens
 */
export const getUserTokens = async (): Promise<UserTokenResponse> => {
  try {
    const cognitoTokens = getCognitoTokensFromCookies();

    if (!cognitoTokens.accessToken) {
      throw new Error("No access token available");
    }

    // Calculate expiresIn from token if available
    let expiresIn = 3600; // Default 1 hour
    if (cognitoTokens.accessToken) {
      try {
        const decoded = jwt.decode(cognitoTokens.accessToken, {
          complete: true,
        });
        if (decoded && typeof decoded !== "string" && decoded.payload) {
          const payload = decoded.payload as any;
          if (payload.exp && payload.iat) {
            expiresIn = payload.exp - payload.iat;
          }
        }
      } catch (error) {
        console.warn("Could not decode token for expiry calculation");
      }
    }

    return {
      accessToken: cognitoTokens.accessToken,
      refreshToken: cognitoTokens.refreshToken || "",
      idToken: cognitoTokens.idToken || "",
      expiresIn,
      tokenType: "Bearer",
    };
  } catch (error) {
    console.error("Error getting user tokens:", error);
    throw new Error("Failed to get user tokens");
  }
};

/**
 * Get the current user's profile information
 */
export const getUserProfile = async (): Promise<UserProfileResponse> => {
  try {
    const response = await fetch("/api/auth/profile", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to get user profile");
    }

    return result;
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw new Error("Failed to get user profile");
  }
};

/**
 * Copy token to clipboard
 */
export const copyTokenToClipboard = async (token: string): Promise<void> => {
  try {
    await navigator.clipboard.writeText(token);
  } catch (error) {
    console.error("Error copying token to clipboard:", error);
    throw new Error("Failed to copy token to clipboard");
  }
};
