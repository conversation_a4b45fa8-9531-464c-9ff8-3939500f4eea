import { put, call, takeLatest, all, select } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import {
  fetchCurrentAdminUser,
  updateAdminUser,
  deleteAdminUser,
  createAdminUser,
  CreateAdminUserResponse,
  fetchAdminUserByUuid,
} from "@/services/adminAPI";
import { adminUsersService } from "@/services/adminUsers";
import { showToast } from "@/utils/toast";
import {
  fetchAdminUserRequest,
  fetchAdminUserSuccess,
  fetchAdminUserFailure,
  fetchSingleAdminUserRequest,
  fetchSingleAdminUserSuccess,
  fetchSingleAdminUserFailure,
  fetchAdminUsersListRequest,
  fetchAdminUsersListSuccess,
  fetchAdminUsersListFailure,
  createAdminUserRequest,
  createAdminUserSuccess,
  createAdminUserFailure,
  updateAdminUserRequest,
  updateAdminUserSuccess,
  updateAdminUserFailure,
  deleteAdminUserRequest,
  deleteAdminUserSuccess,
  deleteAdminUserFailure,
  shouldRefreshAdminUsers,
  AdminUserState,
} from "./redux";
import {
  Admin,
  AdminUserResponse,
  UpdateAdminUserResponse,
  CreateAdminUserRequest,
} from "@/types/adminUser";
import { RootState } from "../index";

function* fetchAdminUserSaga(): Generator<any, void, any> {
  try {
    const response: AdminUserResponse = yield call(fetchCurrentAdminUser);
    if (response && response.success && response.user) {
      yield put(fetchAdminUserSuccess(response.user));
    } else {
      yield put(
        fetchAdminUserFailure(response.message || "Failed to fetch admin user")
      );
    }
  } catch (error: any) {
    yield put(
      fetchAdminUserFailure(error.message || "Failed to fetch admin user")
    );
  }
}

function* fetchSingleAdminUserSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const uuid = action.payload;

    // Always fetch from API, bypassing cache
    const response: Admin = yield call(fetchAdminUserByUuid as any, uuid);
    console.log("Fetched admin user:", response);

    if (response) {
      yield put(fetchSingleAdminUserSuccess(response));
    } else {
      yield put(fetchSingleAdminUserFailure("Failed to fetch admin user"));
    }
  } catch (error: any) {
    // Error handling...
  }
}

function* fetchAdminUsersListSaga(
  action?: PayloadAction<{
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    filters?: any;
  }>
): Generator<any, void, any> {
  try {
    // Get current pagination state
    const adminUserState: any = yield select(
      (state: RootState) => state.adminUser
    );

    const params = {
      page: action?.payload?.page || adminUserState.pagination.currentPage,
      pageSize: action?.payload?.pageSize || adminUserState.pagination.pageSize,
      sortBy: action?.payload?.sortBy || "username",
      sortOrder: action?.payload?.sortOrder || "asc",
      filters: action?.payload?.filters || {
        search: "",
        role: "all",
      },
    };

    const response: any = yield call(adminUsersService.getAdminUsers, params);

    if (response && response.adminUsers) {
      // Transform the response to match AdminUser interface
      const adminUsers = response.adminUsers.map((user: any) => ({
        ...user,
        uuid: user.uuid || user.id,
        // Ensure roles is always an array for consistency
        roles: Array.isArray(user.roles) ? user.roles : [user.roles],
        // Ensure required fields have defaults
        firstName: user.firstName || null,
        lastName: user.lastName || null,
        phone: user.phone || null,
        countrycode: user.countrycode || null,
        permissions: user.permissions || [],
      }));

      yield put(
        fetchAdminUsersListSuccess({
          adminUsers,
          pagination: response.pagination,
        })
      );
    } else {
      yield put(fetchAdminUsersListFailure("Failed to fetch admin users list"));
    }
  } catch (error: any) {
    yield put(
      fetchAdminUsersListFailure(
        error.message || "Failed to fetch admin users list"
      )
    );
  }
}

function* createAdminUserSaga(
  action: PayloadAction<CreateAdminUserRequest>
): Generator<any, void, any> {
  let loadingToastId: string | null = null;

  try {
    // Show initial loading toast
    loadingToastId = yield call(showToast.loading, "Creating admin user...");
    console.log("🚀 Starting admin user creation process");

    const response: any = yield call(createAdminUser as any, action.payload);
    console.log("✅ Admin user creation API response:", response);

    // Handle the actual API response structure:
    // {status_code: 201, success: true, message: "...", uuid: "...", username: "...", email: "...", emailSent: false, isTempPassword: true}
    if (response && (response.status_code === 201 || response.success)) {
      // Dismiss loading toast
      if (loadingToastId) {
        showToast.dismiss(loadingToastId);
      }

      // Transform the response to match AdminUser interface
      const newUser: Admin = {
        uuid: response.uuid,
        username: response.username,
        email: response.email,
        firstName: null, // API doesn't return these fields in create response
        lastName: null,
        phone: null,
        countrycode: null,
        isactive: true, // New users are active by default
        istemppassword: response.isTempPassword || false,
        emailVerified: false,
        roles: action.payload.roles || ["admin"], // Use the roles from request
        createdBy: "", // Not provided in response
        permissions: [], // Will be populated based on role
      };

      // Show success toast with details
      const successMessage = response.emailSent
        ? `Admin user created successfully! Welcome email sent to ${response.email}`
        : `Admin user created successfully! Email: ${response.email}`;

      yield call(showToast.success, successMessage, { duration: 4000 });
      console.log("🎉 Admin user creation completed successfully");

      yield put(createAdminUserSuccess(newUser));
    } else {
      // Handle error response
      if (loadingToastId) {
        showToast.dismiss(loadingToastId);
      }

      const errorMessage =
        response?.error || response?.message || "Failed to create admin user";
      yield call(
        showToast.error,
        `Failed to create admin user: ${errorMessage}`,
        { duration: 6000 }
      );
      console.error("❌ Admin user creation failed:", errorMessage);

      yield put(createAdminUserFailure(errorMessage));
    }
  } catch (error: any) {
    console.error("❌ Error in createAdminUserSaga:", error);

    // Dismiss loading toast and show error
    if (loadingToastId) {
      showToast.dismiss(loadingToastId);
    }

    const errorMessage = error.message || "Failed to create admin user";
    yield call(
      showToast.error,
      `Failed to create admin user: ${errorMessage}`,
      { duration: 6000 }
    );

    yield put(createAdminUserFailure(errorMessage));
  }
}

function* updateAdminUserSaga(
  action: PayloadAction<{ uuid: string; data: any }>
): Generator<any, void, any> {
  try {
    const { uuid, data } = action.payload;
    const response: UpdateAdminUserResponse = yield call(
      updateAdminUser,
      uuid,
      data
    );

    // Handle direct API response structure (the API returns the updated user object directly)
    if (response && response.uuid) {
      // Transform the response to match AdminUser interface
      const updatedUser = {
        uuid: response.uuid,
        username: response.username || "",
        email: response.email || "",
        firstName: response.firstName || null,
        lastName: response.lastName || null,
        phone: response.phone || null,
        countrycode: response.countrycode || null,
        isactive: response.isactive || false,
        istemppassword: response.istemppassword || false,
        emailVerified: response.emailVerified || false,
        roles: response.roles || [],
        createdBy: response.createdBy || "",
        permissions: [], // Add empty permissions array for compatibility
      };

      yield put(updateAdminUserSuccess(updatedUser as any));
    } else if (response && response.success === false) {
      // Handle error response
      yield put(
        updateAdminUserFailure(response.error || "Failed to update admin user")
      );
    } else {
      // Handle unexpected response format
      yield put(updateAdminUserFailure("Unexpected response format"));
    }
  } catch (error: any) {
    yield put(
      updateAdminUserFailure(error.message || "Failed to update admin user")
    );
  }
}

function* deleteAdminUserSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const uuid = action.payload;
    const response: any = yield call(deleteAdminUser, uuid);

    if (response && response.success) {
      yield put(deleteAdminUserSuccess(uuid));
    } else {
      yield put(
        deleteAdminUserFailure(response.error || "Failed to delete admin user")
      );
    }
  } catch (error: any) {
    yield put(
      deleteAdminUserFailure(error.message || "Failed to delete admin user")
    );
  }
}

export function* adminUserSaga() {
  yield all([
    takeLatest(fetchAdminUserRequest.type, fetchAdminUserSaga),
    takeLatest(fetchSingleAdminUserRequest.type, fetchSingleAdminUserSaga),
    takeLatest(fetchAdminUsersListRequest.type, fetchAdminUsersListSaga),
    takeLatest(createAdminUserRequest.type, createAdminUserSaga),
    takeLatest(updateAdminUserRequest.type, updateAdminUserSaga),
    takeLatest(deleteAdminUserRequest.type, deleteAdminUserSaga),
  ]);
}
