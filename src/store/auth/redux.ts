import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  AuthState,
  User,
  LoginFormData,
  RegisterFormData,
  ForgotPasswordFormData,
  ResetPasswordFormData,
  LoginFormValues,
  SocialLoginRequest,
} from "@/types/auth";

interface LoginState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

interface SocialLoginState {
  isLoading: boolean;
  error: string | null;
  redirectUrl: string | null;
}

interface CodeValidationState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

interface TempRegistrationState {
  username: string | null;
  password: string;
  email: string | null;
  isAwaitingTOTP: boolean;
}

interface TotpSetupState {
  required: boolean;
  sharedSecret?: string;
}

interface EmailVerificationState {
  required: boolean;
  username: string;
  password: string;
}

interface ForgotPasswordState {
  isLoading: boolean;
  error: string | null;
  step: "initial" | "verification" | "completed";
  username: string;
  codeDeliveryDetails: any;
}

const initialState: AuthState & {
  login: LoginState;
  socialLogin: SocialLoginState;
  codeValidation: CodeValidationState;
  tempRegistration: TempRegistrationState;
  mfaRequired: boolean;
  mfaType: string | null;
  totpSetup: TotpSetupState;
  emailVerification: EmailVerificationState;
  forgotPassword: ForgotPasswordState;
} = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  requiresMFA: false,
  mfaChallengeType: undefined,
  login: {
    isLoading: false,
    error: null,
    success: false,
  },
  socialLogin: {
    isLoading: false,
    error: null,
    redirectUrl: null,
  },
  codeValidation: {
    isLoading: false,
    error: null,
    success: false,
  },
  tempRegistration: {
    username: "",
    password: "",
    email: "",
    isAwaitingTOTP: false,
  },
  mfaRequired: false,
  mfaType: null,
  totpSetup: { required: false },
  emailVerification: { required: false, username: "", password: "" },
  forgotPassword: {
    isLoading: false,
    error: null,
    step: "initial",
    username: "",
    codeDeliveryDetails: null,
  },
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    loginRequest: (state, action: PayloadAction<LoginFormData>) => {
      state.isLoading = true;
      state.error = null;
      state.mfaRequired = false;
      state.mfaType = null;
      state.totpSetup = { required: false };
    },
    loginSuccess: (state, action: PayloadAction<any>) => {
      state.isLoading = false;
      // If login result indicates MFA/TOTP required, set state accordingly
      if (
        action.payload &&
        action.payload.isSignedIn === false &&
        action.payload.nextStep
      ) {
        state.mfaRequired = true;
        if (
          action.payload.nextStep.signInStep ===
          "CONFIRM_SIGN_IN_WITH_TOTP_CODE"
        ) {
          state.mfaType = "TOTP";
        } else {
          // Only allow TOTP for MFA, force everything else to email verification
          state.mfaType = action.payload.nextStep.signInStep;
        }
        state.isAuthenticated = false;
        state.user = null;
      } else {
        state.isAuthenticated = true;
        state.user = action.payload;
        state.mfaRequired = false;
        state.mfaType = null;
      }
      state.error = null;
      state.requiresMFA = false;
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.error = action.payload;
      state.mfaRequired = false;
      state.mfaType = null;
    },
    loginMFARequired: (state, action: PayloadAction<{ mfaType: string }>) => {
      state.mfaRequired = true;
      state.mfaType = action.payload.mfaType;
      state.isLoading = false;
    },
    setTotpSetup: (state, action: PayloadAction<TotpSetupState>) => {
      state.totpSetup = action.payload;
    },

    // New login form actions for TanStack Query integration
    loginFormRequest: (state, action: PayloadAction<LoginFormValues>) => {
      state.login.isLoading = true;
      state.login.error = null;
      state.login.success = false;
    },
    loginFormSuccess: (
      state,
      action: PayloadAction<{ token: string; user?: User }>
    ) => {
      state.login.isLoading = false;
      state.login.error = null;
      state.login.success = true;
      state.isAuthenticated = true;
      if (action.payload.user) {
        state.user = action.payload.user;
      }
    },
    loginFormFailure: (state, action: PayloadAction<string>) => {
      state.login.isLoading = false;
      state.login.error = action.payload;
      state.login.success = false;
    },

    // Social login actions
    socialLoginRequest: (state, action: PayloadAction<SocialLoginRequest>) => {
      state.socialLogin.isLoading = true;
      state.socialLogin.error = null;
      state.socialLogin.redirectUrl = null;
    },
    socialLoginSuccess: (state, action: PayloadAction<{ url: string }>) => {
      state.socialLogin.isLoading = false;
      state.socialLogin.error = null;
      state.socialLogin.redirectUrl = action.payload.url;
    },
    socialLoginFailure: (state, action: PayloadAction<string>) => {
      state.socialLogin.isLoading = false;
      state.socialLogin.error = action.payload;
      state.socialLogin.redirectUrl = null;
    },

    // Code validation actions
    codeValidationRequest: (state, action: PayloadAction<string>) => {
      state.codeValidation.isLoading = true;
      state.codeValidation.error = null;
      state.codeValidation.success = false;
    },
    codeValidationSuccess: (
      state,
      action: PayloadAction<{ token: string; user?: User }>
    ) => {
      state.codeValidation.isLoading = false;
      state.codeValidation.error = null;
      state.codeValidation.success = true;
      state.isAuthenticated = true;
      if (action.payload.user) {
        state.user = action.payload.user;
      }
    },
    codeValidationFailure: (state, action: PayloadAction<string>) => {
      state.codeValidation.isLoading = false;
      state.codeValidation.error = action.payload;
      state.codeValidation.success = false;
    },

    // MFA actions
    completeMFARequest: (
      state,
      action: PayloadAction<{ code: string; rememberDevice?: boolean }>
    ) => {
      state.isLoading = true;
      state.error = null;
    },
    completeMFASuccess: (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload;
      state.requiresMFA = false;
      state.mfaChallengeType = undefined;
      state.error = null;
    },
    completeMFAFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },

    // Logout actions
    logoutRequest: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    logoutSuccess: (state) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.error = null;
      state.requiresMFA = false;
      state.mfaChallengeType = undefined;
      // Reset login states
      state.login = initialState.login;
      state.socialLogin = initialState.socialLogin;
      state.codeValidation = initialState.codeValidation;
    },
    logoutFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },

    // Refresh user actions
    refreshUserRequest: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    refreshUserSuccess: (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.error = null;
    },
    refreshUserFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
      state.login.error = null;
      state.socialLogin.error = null;
      state.codeValidation.error = null;
    },

    // Set loading
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Reset login form state
    resetLoginForm: (state) => {
      state.login = initialState.login;
    },

    // Reset social login state
    resetSocialLogin: (state) => {
      state.socialLogin = initialState.socialLogin;
    },

    // Reset code validation state
    resetCodeValidation: (state) => {
      state.codeValidation = initialState.codeValidation;
    },

    // Temporary registration actions
    setTempRegistrationCredentials: (
      state,
      action: PayloadAction<{
        username: string;
        password: string;
        email: string;
      }>
    ) => {
      state.tempRegistration.username = action.payload.username;
      state.tempRegistration.password = action.payload.password;
      state.tempRegistration.email = action.payload.email;
      state.tempRegistration.isAwaitingTOTP = true;
    },
    clearTempRegistrationCredentials: (state) => {
      state.tempRegistration = initialState.tempRegistration;
    },
    confirmMFARequest: (
      state,
      _action: PayloadAction<{ username: string; code: string }>
    ) => {
      state.isLoading = true;
      state.error = null;
    },
    confirmMFASuccess: (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload;
      state.mfaRequired = false;
      state.mfaType = null;
      state.error = null;
    },
    confirmMFAFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    setupTOTPRequest: (state, _action: PayloadAction<{ code: string }>) => {
      state.isLoading = true;
      state.error = null;
    },
    setupTOTPSuccess: (state) => {
      state.isLoading = false;
      state.totpSetup = { required: false };
      state.error = null;
    },
    setupTOTPFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },

    // Email verification actions
    setEmailVerificationRequired: (
      state,
      action: PayloadAction<{ username: string; password: string }>
    ) => {
      state.emailVerification.required = true;
      state.emailVerification.username = action.payload.username;
      state.emailVerification.password = action.payload.password;
      state.isLoading = false;
      state.error = null;
    },
    confirmEmailVerificationRequest: (
      state,
      action: PayloadAction<{ username: string; code: string }>
    ) => {
      state.isLoading = true;
      state.error = null;
    },
    confirmEmailVerificationSuccess: (state) => {
      state.isLoading = false;
      state.emailVerification = { required: false, username: "", password: "" };
      state.error = null;
      // After email verification, show TOTP setup
      state.totpSetup = { required: true };
    },
    confirmEmailVerificationFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    resendEmailVerificationRequest: (
      state,
      action: PayloadAction<{ username: string }>
    ) => {
      state.isLoading = true;
      state.error = null;
    },
    resendEmailVerificationSuccess: (state) => {
      state.isLoading = false;
      state.error = null;
    },
    resendEmailVerificationFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },

    // Forgot Password actions
    initiateForgotPasswordRequest: (
      state,
      action: PayloadAction<ForgotPasswordFormData>
    ) => {
      state.forgotPassword.isLoading = true;
      state.forgotPassword.error = null;
      state.forgotPassword.step = "initial";
    },
    initiateForgotPasswordSuccess: (
      state,
      action: PayloadAction<{ username: string; codeDeliveryDetails: any }>
    ) => {
      state.forgotPassword.isLoading = false;
      state.forgotPassword.error = null;
      state.forgotPassword.step = "verification";
      state.forgotPassword.username = action.payload.username;
      state.forgotPassword.codeDeliveryDetails =
        action.payload.codeDeliveryDetails;
    },
    initiateForgotPasswordFailure: (state, action: PayloadAction<string>) => {
      state.forgotPassword.isLoading = false;
      state.forgotPassword.error = action.payload;
      state.forgotPassword.step = "initial";
    },
    confirmForgotPasswordRequest: (
      state,
      action: PayloadAction<ResetPasswordFormData>
    ) => {
      state.forgotPassword.isLoading = true;
      state.forgotPassword.error = null;
    },
    confirmForgotPasswordSuccess: (state) => {
      state.forgotPassword.isLoading = false;
      state.forgotPassword.error = null;
      state.forgotPassword.step = "completed";
    },
    confirmForgotPasswordFailure: (state, action: PayloadAction<string>) => {
      state.forgotPassword.isLoading = false;
      state.forgotPassword.error = action.payload;
    },
    resetForgotPassword: (state) => {
      state.forgotPassword = initialState.forgotPassword;
    },
  },
});

export const {
  loginRequest,
  loginSuccess,
  loginFailure,
  loginMFARequired,
  setTotpSetup,
  loginFormRequest,
  loginFormSuccess,
  loginFormFailure,
  socialLoginRequest,
  socialLoginSuccess,
  socialLoginFailure,
  codeValidationRequest,
  codeValidationSuccess,
  codeValidationFailure,
  completeMFARequest,
  completeMFASuccess,
  completeMFAFailure,
  logoutRequest,
  logoutSuccess,
  logoutFailure,
  refreshUserRequest,
  refreshUserSuccess,
  refreshUserFailure,
  clearError,
  setLoading,
  resetLoginForm,
  resetSocialLogin,
  resetCodeValidation,
  setTempRegistrationCredentials,
  clearTempRegistrationCredentials,
  confirmMFARequest,
  confirmMFASuccess,
  confirmMFAFailure,
  setupTOTPRequest,
  setupTOTPSuccess,
  setupTOTPFailure,
  setEmailVerificationRequired,
  confirmEmailVerificationRequest,
  confirmEmailVerificationSuccess,
  confirmEmailVerificationFailure,
  resendEmailVerificationRequest,
  resendEmailVerificationSuccess,
  resendEmailVerificationFailure,
  initiateForgotPasswordRequest,
  initiateForgotPasswordSuccess,
  initiateForgotPasswordFailure,
  confirmForgotPasswordRequest,
  confirmForgotPasswordSuccess,
  confirmForgotPasswordFailure,
  resetForgotPassword,
} = authSlice.actions;

export default authSlice.reducer;
