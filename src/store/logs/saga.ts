import { all, call, put, takeLatest } from "redux-saga/effects";
import { logsActions } from "./redux";
import { logApiService } from "../../services/logApiService";

// Saga functions
function* fetchLogsSaga(
  action: ReturnType<typeof logsActions.fetchLogs>
): Generator<any, any, any> {
  try {
    const filterParams = action.payload;
    const response = yield call(logApiService.getLogs, filterParams);

    yield put(
      logsActions.fetchLogsSuccess({
        logs: response.logs,
        pagination: {
          page: response.page,
          page_size: response.page_size,
          total_count: response.total_count,
        },
      })
    );
  } catch (error: any) {
    yield put(
      logsActions.fetchLogsFailure(error.message || "Failed to fetch logs")
    );
  }
}

function* fetchMyLogsSaga(
  action: ReturnType<typeof logsActions.fetchMyLogs>
): Generator<any, any, any> {
  try {
    const filterParams = action.payload;
    const response = yield call(logApiService.getMyLogs, filterParams);

    yield put(
      logsActions.fetchMyLogsSuccess({
        logs: response.data.logs,
        pagination: {
          page: response.data.page,
          page_size: response.data.page_size,
          total_count: response.data.total_count,
        },
      })
    );
  } catch (error: any) {
    yield put(
      logsActions.fetchMyLogsFailure(error.message || "Failed to fetch my logs")
    );
  }
}

function* exportLogsSaga(
  action: ReturnType<typeof logsActions.exportLogs>
): Generator<any, any, any> {
  try {
    const exportRequest = action.payload;
    const response = yield call(logApiService.exportLogs, exportRequest);
    // Download the CSV file
    yield call(
      logApiService.downloadCSV,
      response.message.csv_content,
      response.message.filename
    );

    yield put(
      logsActions.exportLogsSuccess({
        csv_content: response.csv_content,
        filename: response.filename,
        export_timestamp: response.export_timestamp,
      })
    );
  } catch (error: any) {
    yield put(
      logsActions.exportLogsFailure(error.message || "Failed to export logs")
    );
  }
}

function* fetchActionsSaga(): Generator<any, any, any> {
  try {
    const response = yield call(logApiService.getActions);

    yield put(logsActions.fetchActionsSuccess(response.actions));
  } catch (error: any) {
    yield put(
      logsActions.fetchActionsFailure(
        error.message || "Failed to fetch available actions"
      )
    );
  }
}

function* fetchExportFieldsSaga(): Generator<any, any, any> {
  try {
    const response = yield call(logApiService.getExportFields);

    yield put(logsActions.fetchExportFieldsSuccess(response.data.fields));
  } catch (error: any) {
    yield put(
      logsActions.fetchExportFieldsFailure(
        error.message || "Failed to fetch export fields"
      )
    );
  }
}

// Root saga
export function* logsSaga() {
  yield all([
    takeLatest(logsActions.fetchLogs.type, fetchLogsSaga),
    takeLatest(logsActions.fetchMyLogs.type, fetchMyLogsSaga),
    takeLatest(logsActions.exportLogs.type, exportLogsSaga),
    takeLatest(logsActions.fetchActions.type, fetchActionsSaga),
    takeLatest(logsActions.fetchExportFields.type, fetchExportFieldsSaga),
  ]);
}
