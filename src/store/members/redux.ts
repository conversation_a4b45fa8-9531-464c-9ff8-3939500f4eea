import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  Member,
  MemberWithRelations,
  CreateMemberData,
  UpdateMemberData,
  AuthenticationMetrics,
} from "@/types/member";

// Member metrics interface
export interface MemberMetrics {
  totalMembers: number;
  liteMembers: number;
  fullMembers: number;
  activeMembers: number;
  periodLabel: string;
}

export interface MemberMetricsRequest {
  startDate?: string;
  endDate?: string;
}

interface PaginationState {
  totalCount: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

interface MembersState {
  members: Member[];
  membersById: Record<string, Member>;
  currentMember: MemberWithRelations | null;
  loading: boolean;
  error: string | null;
  lastFetched: string | null;
  // Pagination state
  pagination: PaginationState;
  // Filter state
  filters: {
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    search?: string;
    id?: number;
    uuid?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    membershipTier?: string;
    organizationName?: string;
    organizationCity?: string;
    organizationState?: string;
    organizationZip?: string;
    companySize?: string;
    industry?: string;
    dateCreatedFrom?: string;
    dateCreatedTo?: string;
    hasOrganizations?: boolean;
  };
  // Single member operations
  singleMemberLoading: boolean;
  singleMemberError: string | null;
  createMemberLoading: boolean;
  createMemberError: string | null;
  updateMemberLoading: boolean;
  updateMemberError: string | null;
  deleteMemberLoading: boolean;
  deleteMemberError: string | null;
  bulkDeleteLoading: boolean;
  bulkDeleteError: string | null;
  // Member metrics state
  metrics: MemberMetrics | null;
  metricsLoading: boolean;
  metricsError: string | null;
  metricsLastFetched: string | null;
  currentMetricsRequest: MemberMetricsRequest | null;
  // Comparison metrics state
  comparisonMetrics: MemberMetrics | null;
  comparisonMetricsLoading: boolean;
  comparisonMetricsError: string | null;
  comparisonMetricsLastFetched: string | null;
  currentComparisonMetricsRequest: MemberMetricsRequest | null;
  // Authentication metrics state
  authenticationMetrics: AuthenticationMetrics | null;
  authenticationMetricsLoading: boolean;
  authenticationMetricsError: string | null;
  authenticationMetricsLastFetched: string | null;
}

const initialState: MembersState = {
  members: [],
  membersById: {},
  currentMember: null,
  loading: false,
  error: null,
  lastFetched: null,
  pagination: {
    totalCount: 0,
    currentPage: 1,
    pageSize: 25,
    totalPages: 0,
    hasNext: false,
    hasPrevious: false,
  },
  filters: {
    page: 1,
    pageSize: 25,
    sortBy: "dateUpdated",
    sortOrder: "desc",
  },
  singleMemberLoading: false,
  singleMemberError: null,
  createMemberLoading: false,
  createMemberError: null,
  updateMemberLoading: false,
  updateMemberError: null,
  deleteMemberLoading: false,
  deleteMemberError: null,
  bulkDeleteLoading: false,
  bulkDeleteError: null,
  // Member metrics initial state
  metrics: null,
  metricsLoading: false,
  metricsError: null,
  metricsLastFetched: null,
  currentMetricsRequest: null,
  // Comparison metrics initial state
  comparisonMetrics: null,
  comparisonMetricsLoading: false,
  comparisonMetricsError: null,
  comparisonMetricsLastFetched: null,
  currentComparisonMetricsRequest: null,
  // Authentication metrics initial state
  authenticationMetrics: null,
  authenticationMetricsLoading: false,
  authenticationMetricsError: null,
  authenticationMetricsLastFetched: null,
};

const membersSlice = createSlice({
  name: "members",
  initialState,
  reducers: {
    // List operations
    fetchMembersRequest(
      state,
      action: PayloadAction<
        | {
            page?: number;
            pageSize?: number;
            sortBy?: string;
            sortOrder?: "asc" | "desc";
            filters?: any;
          }
        | undefined
      >
    ) {
      state.loading = true;
      state.error = null;
    },
    fetchMembersSuccess(
      state,
      action: PayloadAction<{
        members: Member[];
        pagination: PaginationState;
      }>
    ) {
      state.loading = false;
      state.members = action.payload.members;
      state.pagination = action.payload.pagination;
      state.membersById = {};
      action.payload.members.forEach((m) => {
        state.membersById[m.uuid] = m;
      });
      state.lastFetched = new Date().toISOString();
      state.error = null;
    },
    fetchMembersFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },

    // Single member operations
    fetchMemberRequest(state, action: PayloadAction<string>) {
      state.singleMemberLoading = true;
      state.singleMemberError = null;
    },
    fetchMemberSuccess(state, action: PayloadAction<MemberWithRelations>) {
      state.singleMemberLoading = false;
      state.currentMember = action.payload;
      state.singleMemberError = null;
    },
    fetchMemberFailure(state, action: PayloadAction<string>) {
      state.singleMemberLoading = false;
      state.singleMemberError = action.payload;
    },

    // Create member operations
    createMemberRequest(state, action: PayloadAction<CreateMemberData>) {
      state.createMemberLoading = true;
      state.createMemberError = null;
    },
    createMemberSuccess(state, action: PayloadAction<Member>) {
      state.createMemberLoading = false;
      state.members.push(action.payload);
      state.membersById[action.payload.uuid] = action.payload;
      state.createMemberError = null;
    },
    createMemberFailure(state, action: PayloadAction<string>) {
      state.createMemberLoading = false;
      state.createMemberError = action.payload;
    },

    // Update member operations
    updateMemberRequest(
      state,
      action: PayloadAction<{ id: string; data: UpdateMemberData }>
    ) {
      state.updateMemberLoading = true;
      state.updateMemberError = null;
    },
    updateMemberSuccess(state, action: PayloadAction<Member>) {
      state.updateMemberLoading = false;
      const index = state.members.findIndex(
        (m) => m.uuid === action.payload.uuid
      );
      if (index !== -1) {
        state.members[index] = action.payload;
      }
      state.membersById[action.payload.uuid] = action.payload;
      if (
        state.currentMember &&
        state.currentMember.uuid === action.payload.uuid
      ) {
        state.currentMember = { ...state.currentMember, ...action.payload };
      }
      state.updateMemberError = null;
    },
    updateMemberFailure(state, action: PayloadAction<string>) {
      state.updateMemberLoading = false;
      state.updateMemberError = action.payload;
    },

    // Delete member operations
    deleteMemberRequest(state) {
      state.deleteMemberLoading = true;
      state.deleteMemberError = null;
    },
    deleteMemberSuccess(state, action: PayloadAction<string>) {
      state.deleteMemberLoading = false;
      state.members = state.members.filter(
        (m) => String(m.uuid) !== action.payload
      );
      delete state.membersById[action.payload];
      if (
        state.currentMember &&
        String(state.currentMember.uuid) === action.payload
      ) {
        state.currentMember = null;
      }
      state.deleteMemberError = null;
    },
    deleteMemberFailure(state, action: PayloadAction<string>) {
      state.deleteMemberLoading = false;
      state.deleteMemberError = action.payload;
    },

    // Bulk delete member operations
    bulkDeleteMembersRequest(state, action: PayloadAction<string[]>) {
      state.bulkDeleteLoading = true;
      state.bulkDeleteError = null;
    },
    bulkDeleteMembersSuccess(state, action: PayloadAction<string[]>) {
      state.bulkDeleteLoading = false;
      // Remove deleted members from state
      action.payload.forEach((memberId) => {
        state.members = state.members.filter(
          (m) => String(m.uuid) !== memberId
        );
        delete state.membersById[memberId];
        if (
          state.currentMember &&
          String(state.currentMember.uuid) === memberId
        ) {
          state.currentMember = null;
        }
      });
      state.bulkDeleteError = null;
    },
    bulkDeleteMembersFailure(state, action: PayloadAction<string>) {
      state.bulkDeleteLoading = false;
      state.bulkDeleteError = action.payload;
    },

    // Export operations
    exportMembersRequest(
      state,
      action: PayloadAction<{
        filters: any;
        selectedFields: string[];
        notes: string;
        sortBy?: string;
        sortOrder?: "asc" | "desc";
      }>
    ) {
      // state.loading = true;
      state.error = null;
    },
    exportMembersSuccess(state, action: PayloadAction<{ jobId: string; message: string }>) {
      state.loading = false;
      state.error = null;
    },
    exportMembersFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },

    // Filter actions
    setFilters(
      state,
      action: PayloadAction<{
        page?: number;
        pageSize?: number;
        sortBy?: string;
        sortOrder?: "asc" | "desc";
        search?: string;
        uuid?: string;
        firstName?: string;
        lastName?: string;
        email?: string;
        membershipTier?: string;
        organizationName?: string;
        organizationCity?: string;
        organizationState?: string;
        organizationZip?: string;
        companySize?: string;
        industry?: string;
        dateCreatedFrom?: string;
        dateCreatedTo?: string;
        hasOrganizations?: boolean;
      }>
    ) {
      state.filters = { ...state.filters, ...action.payload };
    },

    // Pagination actions
    setCurrentPage(state, action: PayloadAction<number>) {
      state.pagination.currentPage = action.payload;
    },
    setPageSize(state, action: PayloadAction<number>) {
      state.pagination.pageSize = action.payload;
      state.pagination.currentPage = 1; // Reset to first page when changing page size
    },

    // Clear operations
    clearSingleMemberError(state) {
      state.singleMemberError = null;
    },
    clearCreateMemberError(state) {
      state.createMemberError = null;
    },
    clearUpdateMemberError(state) {
      state.updateMemberError = null;
    },

    // Member metrics actions
    fetchMemberMetricsRequest(
      state,
      action: PayloadAction<MemberMetricsRequest>
    ) {
      state.metricsLoading = true;
      state.metricsError = null;
      state.currentMetricsRequest = action.payload;
    },
    fetchMemberMetricsSuccess(state, action: PayloadAction<MemberMetrics>) {
      state.metrics = action.payload;
      state.metricsLoading = false;
      state.metricsError = null;
      state.metricsLastFetched = new Date().toISOString();
    },
    fetchMemberMetricsFailure(state, action: PayloadAction<string>) {
      state.metricsLoading = false;
      state.metricsError = action.payload;
    },
    clearMemberMetricsError(state) {
      state.metricsError = null;
    },

    // Comparison metrics actions
    fetchComparisonMetricsRequest(
      state,
      action: PayloadAction<MemberMetricsRequest>
    ) {
      state.comparisonMetricsLoading = true;
      state.comparisonMetricsError = null;
      state.currentComparisonMetricsRequest = action.payload;
    },
    fetchComparisonMetricsSuccess(state, action: PayloadAction<MemberMetrics>) {
      state.comparisonMetrics = action.payload;
      state.comparisonMetricsLoading = false;
      state.comparisonMetricsError = null;
      state.comparisonMetricsLastFetched = new Date().toISOString();
    },
    fetchComparisonMetricsFailure(state, action: PayloadAction<string>) {
      state.comparisonMetricsLoading = false;
      state.comparisonMetricsError = action.payload;
    },
    clearComparisonMetricsError(state) {
      state.comparisonMetricsError = null;
    },

    // Authentication metrics actions
    fetchAuthenticationMetricsRequest(state) {
      state.authenticationMetricsLoading = true;
      state.authenticationMetricsError = null;
    },
    fetchAuthenticationMetricsSuccess(state, action: PayloadAction<AuthenticationMetrics>) {
      state.authenticationMetrics = action.payload;
      state.authenticationMetricsLoading = false;
      state.authenticationMetricsError = null;
      state.authenticationMetricsLastFetched = new Date().toISOString();
    },
    fetchAuthenticationMetricsFailure(state, action: PayloadAction<string>) {
      state.authenticationMetricsLoading = false;
      state.authenticationMetricsError = action.payload;
    },
    clearAuthenticationMetricsError(state) {
      state.authenticationMetricsError = null;
    },
  },
});

export const {
  // List operations
  fetchMembersRequest,
  fetchMembersSuccess,
  fetchMembersFailure,

  // Single member operations
  fetchMemberRequest,
  fetchMemberSuccess,
  fetchMemberFailure,

  // Create member operations
  createMemberRequest,
  createMemberSuccess,
  createMemberFailure,

  // Update member operations
  updateMemberRequest,
  updateMemberSuccess,
  updateMemberFailure,

  // Delete member operations
  deleteMemberRequest,
  deleteMemberSuccess,
  deleteMemberFailure,

  // Bulk delete member operations
  bulkDeleteMembersRequest,
  bulkDeleteMembersSuccess,
  bulkDeleteMembersFailure,

  // Export operations
  exportMembersRequest,
  exportMembersSuccess,
  exportMembersFailure,

  // Filter actions
  setFilters,

  // Pagination actions
  setCurrentPage,
  setPageSize,

  // Clear operations
  clearSingleMemberError,
  clearCreateMemberError,
  clearUpdateMemberError,

  // Member metrics actions
  fetchMemberMetricsRequest,
  fetchMemberMetricsSuccess,
  fetchMemberMetricsFailure,
  clearMemberMetricsError,

  // Comparison metrics actions
  fetchComparisonMetricsRequest,
  fetchComparisonMetricsSuccess,
  fetchComparisonMetricsFailure,
  clearComparisonMetricsError,

  // Authentication metrics actions
  fetchAuthenticationMetricsRequest,
  fetchAuthenticationMetricsSuccess,
  fetchAuthenticationMetricsFailure,
  clearAuthenticationMetricsError,
} = membersSlice.actions;

export default membersSlice.reducer;

// Selectors
export const selectMembersArray = (state: any) => state.members.members;
export const selectMembersMap = (state: any) => state.members.membersById;
export const selectMembersLoading = (state: any) => state.members.loading;
export const selectMembersError = (state: any) => state.members.error;
export const selectMembersLastFetched = (state: any) =>
  state.members.lastFetched;

// Single member selectors
export const selectCurrentMember = (state: any) => state.members.currentMember;
export const selectSingleMemberLoading = (state: any) =>
  state.members.singleMemberLoading;
export const selectSingleMemberError = (state: any) =>
  state.members.singleMemberError;

// Create member selectors
export const selectCreateMemberLoading = (state: any) =>
  state.members.createMemberLoading;
export const selectCreateMemberError = (state: any) =>
  state.members.createMemberError;

// Update member selectors
export const selectUpdateMemberLoading = (state: any) =>
  state.members.updateMemberLoading;
export const selectUpdateMemberError = (state: any) =>
  state.members.updateMemberError;

// Delete member selectors
export const selectDeleteMemberLoading = (state: any) =>
  state.members.deleteMemberLoading;
export const selectDeleteMemberError = (state: any) =>
  state.members.deleteMemberError;

// Bulk delete member selectors
export const selectBulkDeleteLoading = (state: any) =>
  state.members.bulkDeleteLoading;
export const selectBulkDeleteError = (state: any) =>
  state.members.bulkDeleteError;

// Filter selectors
export const selectMembersFilters = (state: any) => state.members.filters;

// Pagination selectors
export const selectMembersPagination = (state: any) => state.members.pagination;
export const selectMembersCurrentPage = (state: any) =>
  state.members.pagination.currentPage;
export const selectMembersPageSize = (state: any) =>
  state.members.pagination.pageSize;
export const selectMembersTotal = (state: any) =>
  state.members.pagination.totalCount;
export const selectMembersTotalPages = (state: any) =>
  state.members.pagination.totalPages;
export const selectMembersHasNext = (state: any) =>
  state.members.pagination.hasNext;
export const selectMembersHasPrevious = (state: any) =>
  state.members.pagination.hasPrevious;

// Member metrics selectors
export const selectMemberMetrics = (state: any) => state.members.metrics;
export const selectMemberMetricsLoading = (state: any) =>
  state.members.metricsLoading;
export const selectMemberMetricsError = (state: any) =>
  state.members.metricsError;
export const selectMemberMetricsLastFetched = (state: any) =>
  state.members.metricsLastFetched;
export const selectCurrentMetricsRequest = (state: any) =>
  state.members.currentMetricsRequest;

// Comparison metrics selectors
export const selectComparisonMetrics = (state: any) => state.members.comparisonMetrics;
export const selectComparisonMetricsLoading = (state: any) =>
  state.members.comparisonMetricsLoading;
export const selectComparisonMetricsError = (state: any) =>
  state.members.comparisonMetricsError;
export const selectComparisonMetricsLastFetched = (state: any) =>
  state.members.comparisonMetricsLastFetched;
export const selectCurrentComparisonMetricsRequest = (state: any) =>
  state.members.currentComparisonMetricsRequest;

// Authentication metrics selectors
export const selectAuthenticationMetrics = (state: any) => state.members.authenticationMetrics;
export const selectAuthenticationMetricsLoading = (state: any) =>
  state.members.authenticationMetricsLoading;
export const selectAuthenticationMetricsError = (state: any) =>
  state.members.authenticationMetricsError;
export const selectAuthenticationMetricsLastFetched = (state: any) =>
  state.members.authenticationMetricsLastFetched;
