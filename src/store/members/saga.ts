import { PayloadAction } from "@reduxjs/toolkit";
import { all, call, put, takeLatest, select } from "redux-saga/effects";
import { membersService } from "@/services/members";
import {
  assignOrganizationToMember,
  organizationService,
  removeOrganizationFromMember,
} from "@/services/organizationService";
import { showToast } from "@/utils/toast";
import {
  fetchMembersRequest,
  fetchMembersSuccess,
  fetchMembersFailure,
  fetchMemberRequest,
  fetchMemberSuccess,
  fetchMemberFailure,
  createMemberRequest,
  createMemberSuccess,
  createMemberFailure,
  updateMemberRequest,
  updateMemberSuccess,
  updateMemberFailure,
  deleteMemberRequest,
  deleteMemberSuccess,
  deleteMemberFailure,
  bulkDeleteMembersRequest,
  bulkDeleteMembersSuccess,
  bulkDeleteMembersFailure,
  exportMembersRequest,
  exportMembersSuccess,
  exportMembersFailure,
  setFilters,
  selectMembersArray,
  selectMembersLastFetched,
  selectMembersPagination,
  selectMembersFilters,
  // Member metrics actions
  fetchMemberMetricsRequest,
  fetchMemberMetricsSuccess,
  fetchMemberMetricsFailure,
  selectMemberMetrics,
  selectMemberMetricsLastFetched,
  selectCurrentMetricsRequest,
  // Comparison metrics actions
  fetchComparisonMetricsRequest,
  fetchComparisonMetricsSuccess,
  fetchComparisonMetricsFailure,
  selectComparisonMetrics,
  selectComparisonMetricsLastFetched,
  selectCurrentComparisonMetricsRequest,
  // Authentication metrics actions
  fetchAuthenticationMetricsRequest,
  fetchAuthenticationMetricsSuccess,
  fetchAuthenticationMetricsFailure,
  selectAuthenticationMetrics,
  selectAuthenticationMetricsLastFetched,
} from "./redux";
import { CreateMemberData, UpdateMemberData } from "@/lib/validations/member";
import { convertSnakeToCamelCase } from "@/utils/memberUtils";
import { MemberSearchFilters } from "@/types/member";
import { navigate } from "@/utils/routerService";

function* fetchMembersSaga(
  action?: PayloadAction<{
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    filters?: any;
  }>
): Generator<any, void, any> {
  try {
    // console.log("🔄 fetchMembersSaga called with:", action?.payload);
    const currentFilters = yield select(selectMembersFilters);

    // Merge current filters with new filters if provided
    let finalFilters = currentFilters;
    let hasFiltersChanged = false;

    if (action?.payload?.filters) {
      finalFilters = { ...currentFilters, ...action.payload.filters };
      hasFiltersChanged = true;
    }

    // Update sortBy and sortOrder if provided in action payload
    if (action?.payload?.sortBy || action?.payload?.sortOrder) {
      finalFilters = {
        ...finalFilters,
        sortBy: action.payload.sortBy || finalFilters.sortBy,
        sortOrder: action.payload.sortOrder || finalFilters.sortOrder,
      };
      hasFiltersChanged = true;
    }

    // Only update Redux state if filters actually changed
    if (hasFiltersChanged) {
      const hasChanged =
        JSON.stringify(currentFilters) !== JSON.stringify(finalFilters);
      if (hasChanged) {
        yield put(setFilters(finalFilters));
      }
    }

    const params = {
      page: finalFilters.page || 1,
      pageSize: finalFilters.pageSize || 25,
      sortBy: action?.payload?.sortBy || finalFilters.sortBy || "dateUpdated",
      sortOrder: action?.payload?.sortOrder || finalFilters.sortOrder || "desc",
      filters: {
        search: finalFilters.search || "",
        id: finalFilters.id || undefined,
        uuid: finalFilters.uuid || "",
        firstName: finalFilters.firstName || "",
        lastName: finalFilters.lastName || "",
        email: finalFilters.email || "",
        membershipTier: finalFilters.membershipTier || "",
        organizationName: finalFilters.organizationName || "",
        organizationCity: finalFilters.organizationCity || "",
        organizationState: finalFilters.organizationState || "",
        organizationZip: finalFilters.organizationZip || "",
        companySize: finalFilters.companySize || "",
        industry: finalFilters.industry || "",
        dateCreatedFrom: finalFilters.dateCreatedFrom || "",
        dateCreatedTo: finalFilters.dateCreatedTo || "",
        hasOrganizations: finalFilters.hasOrganizations,
        nameFilter: finalFilters.nameFilter,
      },
    };

    // console.log("📡 Making API call with params:", params);
    const response = yield call(membersService.getMembers, params);
    // console.log(
        //   "✅ API call successful, received:",
        //   response.members?.length,
        //   "members"
        // );
    yield put(
      fetchMembersSuccess({
        members: response.members,
        pagination: response.pagination,
      })
    );
  } catch (error: any) {
    console.error("❌ fetchMembersSaga error:", error);

    // Check if it's an authentication error
    if (error.message?.includes("401") || error.message?.includes("Unauthorized")) {
      console.log("🔄 Authentication error detected, will retry after token refresh");
      // Don't immediately fail, let the background refresh handle it
      yield put(fetchMembersFailure("Authentication required. Please wait while we refresh your session..."));
    } else {
      yield put(fetchMembersFailure(error.message || "Failed to fetch members"));
    }
  }
}

function* fetchMemberSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const member = yield call(membersService.getMember, action.payload);
    yield put(fetchMemberSuccess(member));
  } catch (error: any) {
    yield put(fetchMemberFailure(error.message || "Failed to fetch member"));
  }
}

// NEW: Export members saga (asynchronous)
function* exportMembersSaga(
  action: PayloadAction<{
    filters: any;
    selectedFields: string[];
    notes: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }>
): Generator<any, void, any> {
  try {
    // Safely destructure payload with defaults
    const payload = action.payload;
    if (!payload) {
      yield call(showToast.error, "Export data is missing");
      yield put(exportMembersFailure("Export data is missing"));
      return;
    }

    const { filters, selectedFields, notes, sortBy, sortOrder } = payload;

    // Validate required fields
    if (!selectedFields || selectedFields.length === 0) {
      yield call(showToast.error, "Please select at least one field to export");
      yield put(exportMembersFailure("No fields selected for export"));
      return;
    }

    if (!notes || notes.trim() === "") {
      yield call(showToast.error, "Please provide a reason for this export");
      yield put(exportMembersFailure("Export notes are required"));
      return;
    }

    const exportRequest = {
      filters: filters || {},
      selectedFields,
      notes: notes.trim(),
      sortBy: sortBy || "dateUpdated",
      sortOrder: sortOrder || "desc",
    };

    const response = yield call(membersService.exportMembers, exportRequest);

    // Show success message with job ID
    yield put(exportMembersSuccess({ 
      jobId: response.job_id, 
      message: "Export job started successfully" 
    }));
    
    // Show detailed toast with job information
    yield call(showToast.success, `Your export job in queue - It usually takes 2–5 minutes`);
    
    // Navigate to CSV downloads page

  } catch (error: any) {
    yield put(exportMembersFailure(error.message || "Export failed"));
    yield call(showToast.error, error.message || "Export failed");
  }
}

function* createMemberSaga(
  action: PayloadAction<CreateMemberData>
): Generator<any, void, any> {
  try {
    const response = yield call(() =>
      membersService.createMember(action.payload)
    );
    //show tost when response is success
    const org1: any = action.payload;
    yield call(showToast.success, "Member created successfully");
    yield call(navigate, "/members");
    yield put(createMemberSuccess(response));

    // Create organization relations if organizations exist
    if (org1.selectedOrganizations && org1.selectedOrganizations.length > 0) {
      for (const org of org1.selectedOrganizations) {
        yield call(organizationService.createMemberRelation, {
           memberUuid: response.user.uuid,
          organizationUuid: org.uuid || "",
        });
      }
    }
  } catch (error: any) {
    // console.log(error, "error");
    yield call(showToast.error, error.message || "Failed to create member");
    yield put(createMemberFailure(error.message || "Failed to create member"));
  }
}

function* updateMemberSaga(
  action: PayloadAction<{ id: string; data: UpdateMemberData }>
): Generator<any, void, any> {
  try {
    const { id, data } = action.payload;
    const updatedMember = yield call(
      membersService.updateMember,
      id,
      data as any
    );

    // Handle organization relations update if selectedOrganizations is provided
    // console.log("🔍 DEBUG Update: data received:", data);
    // console.log(
    //   "🔍 DEBUG Update: selectedOrganizations:",
    //   (data as any).selectedOrganizations
    // );

    if ((data as any).selectedOrganizations !== undefined) {
      try {
        // console.log(
        //   "🔍 DEBUG Update: Starting organization relations update for member:",
        //   id
        // );

        // Get current member organizations (using the working function)
        const currentOrganizations = yield call(
          organizationService.getOrganizationsByMember,
          id
        );
        // console.log(
        //   "🔍 DEBUG Update: Current organizations:",
        //   currentOrganizations
        // );

        // Get current organization UUIDs from organizations
        const currentOrgUuids = currentOrganizations.map(
          (org: any) => org.uuid
        );
        // console.log("🔍 DEBUG Update: Current org UUIDs:", currentOrgUuids);

        // Get new organization UUIDs
        const newOrgUuids = ((data as any).selectedOrganizations || [])
          .map((org: any) => org.uuid || "")
          .filter(Boolean);
        // console.log("🔍 DEBUG Update: New org UUIDs:", newOrgUuids);

        // // Delete relations that are no longer needed
        const orgsToRemove = currentOrgUuids.filter(
          (orgUuid: any) => !newOrgUuids.includes(orgUuid)
        );

        for (const orgUuid of orgsToRemove) {
          // console.log("🔍 DEBUG Update: Deleting relation:", {
          //   memberUuid: id,
          //   organizationUuid: orgUuid,
          // });
          yield call(removeOrganizationFromMember, id, orgUuid);
        }

        // Create new relations
        for (const orgUuid of newOrgUuids) {
          if (!currentOrgUuids.includes(orgUuid)) {
            // console.log("🔍 DEBUG Update: Creating new relation:", {
            //   memberUuid: id,
            //   organizationUuid: orgUuid,
            // });
            yield call(organizationService.createMemberRelation, {
              memberUuid: id,
              organizationUuid: orgUuid,
            });
          }
        }

        // console.log("🔍 DEBUG Update: Organization relations update completed");

        yield call(navigate, "/members");
      } catch (orgError: any) {
        // console.error(
        //   "🔍 DEBUG Update: Organization relations error:",
        //   orgError
        // );
        // Don't throw - let member update succeed even if org relations fail
        yield call(
          showToast.error,
          `Organization relations update failed: ${orgError.message}`
        );
      }
    }

    yield call(showToast.success, "Member updated successfully");
    yield put(updateMemberSuccess(updatedMember));
  } catch (error: any) {
    yield call(showToast.error, error.message || "Failed to update member");
    yield put(updateMemberFailure(error.message || "Failed to update member"));
  }
}

function* deleteMemberSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const memberId = action.payload;
    yield call(membersService.deleteMember, memberId);
    yield put(deleteMemberSuccess(memberId));
  } catch (error: any) {
    yield put(deleteMemberFailure(error.message || "Failed to delete member"));
  }
}

function* bulkDeleteMembersSaga(
  action: PayloadAction<string[]>
): Generator<any, void, any> {
  try {
    const memberUuids = action.payload;
    const response = yield call(membersService.bulkDeleteMembers, memberUuids);

    // Extract successfully deleted member UUIDs from response
    const successfulDeletes =
      response.data?.results
        ?.filter((result: any) => result.success)
        ?.map((result: any) => result.memberUuid) || memberUuids;

    yield put(bulkDeleteMembersSuccess(successfulDeletes));

    // Show success message with results
    const totalProcessed = response.data?.totalProcessed || memberUuids.length;
    const successful = response.data?.successful || successfulDeletes.length;
    const failed = response.data?.failed || 0;

    if (successful > 0) {
      yield call(
        showToast.success,
        `Successfully deleted ${successful} member(s)`
      );
    }

    if (failed > 0) {
      yield call(showToast.error, `${failed} member(s) could not be deleted`);
    }

    // Refresh the members list
    yield put(fetchMembersRequest());
  } catch (error: any) {
    yield put(
      bulkDeleteMembersFailure(error.message || "Failed to bulk delete members")
    );
    yield call(
      showToast.error,
      error.message || "Failed to bulk delete members"
    );
  }
}

// Member metrics saga
function* fetchMemberMetricsSaga(
  action: PayloadAction<{ startDate?: string; endDate?: string }>
): Generator<any, void, any> {
  try {

    const currentMetrics = yield select(selectMemberMetrics);
    const lastFetched = yield select(selectMemberMetricsLastFetched);
    const currentRequest = yield select(selectCurrentMetricsRequest);



    // Check if we have cached data for the same request
    const isSameRequest =
      currentRequest &&
      currentRequest.startDate === action.payload.startDate &&
      currentRequest.endDate === action.payload.endDate;

    // Check if data is recent (within 5 minutes)
    const isDataRecent =
      lastFetched &&
      new Date().getTime() - new Date(lastFetched).getTime() < 5 * 60 * 1000;



   
    const response = yield call(
      membersService.getMemberMetrics,
      action.payload
    );
    // console.log("✅ Member metrics API call successful:", response);

    yield put(fetchMemberMetricsSuccess(response.data));
  } catch (error: any) {
    // console.error("❌ Member metrics API call failed:", error);
    yield put(
      fetchMemberMetricsFailure(
        error.message || "Failed to fetch member metrics"
      )
    );
  }
}

// Comparison metrics saga
function* fetchComparisonMetricsSaga(
  action: PayloadAction<{ startDate?: string; endDate?: string }>
): Generator<any, void, any> {
  try {

    const currentMetrics = yield select(selectComparisonMetrics);
    const lastFetched = yield select(selectComparisonMetricsLastFetched);
    const currentRequest = yield select(selectCurrentComparisonMetricsRequest);


    // Check if we have cached data for the same request
    const isSameRequest =
      currentRequest &&
      currentRequest.startDate === action.payload.startDate &&
      currentRequest.endDate === action.payload.endDate;

    // Check if data is recent (within 5 minutes)
    const isDataRecent =
      lastFetched &&
      new Date().getTime() - new Date(lastFetched).getTime() < 5 * 60 * 1000;


    // For now, let's disable caching to ensure API calls are made
    // if (currentMetrics && isSameRequest && isDataRecent) {
    //   console.log("📦 Using cached comparison metrics data");
    //   return;
    // }

    const response = yield call(
      membersService.getComparisonMetrics,
      action.payload
    );
    console.log("✅ Comparison metrics API call successful:", response);

    yield put(fetchComparisonMetricsSuccess(response.data));
  } catch (error: any) {
    console.error("❌ Comparison metrics API call failed:", error);
    yield put(
      fetchComparisonMetricsFailure(
        error.message || "Failed to fetch comparison metrics"
      )
    );
  }
}

// Authentication metrics saga
function* fetchAuthenticationMetricsSaga(): Generator<any, void, any> {
  try {

    const currentMetrics = yield select(selectAuthenticationMetrics);
    const lastFetched = yield select(selectAuthenticationMetricsLastFetched);


    // Check if data is recent (within 5 minutes)
    const isDataRecent =
      lastFetched &&
      new Date().getTime() - new Date(lastFetched).getTime() < 5 * 60 * 1000;



    // For now, let's disable caching to ensure API calls are made
    // if (currentMetrics && isDataRecent) {
    //   console.log("📦 Using cached authentication metrics data");
    //   return;
    // }


    const response = yield call(membersService.getAuthenticationMetrics);


    yield put(fetchAuthenticationMetricsSuccess(response.data));
  } catch (error: any) {

    yield put(
      fetchAuthenticationMetricsFailure(
        error.message || "Failed to fetch authentication metrics"
      )
    );
  }
}

export default function* membersSaga() {
  yield all([
    takeLatest(fetchMembersRequest.type, fetchMembersSaga),
    takeLatest(fetchMemberRequest.type, fetchMemberSaga),
    takeLatest(createMemberRequest.type, createMemberSaga),
    takeLatest(updateMemberRequest.type, updateMemberSaga),
    takeLatest(deleteMemberRequest.type, deleteMemberSaga),
    takeLatest(bulkDeleteMembersRequest.type, bulkDeleteMembersSaga),
    takeLatest(exportMembersRequest.type, exportMembersSaga),
    // Member metrics saga
    takeLatest(fetchMemberMetricsRequest.type, fetchMemberMetricsSaga),
    // Comparison metrics saga
    takeLatest(fetchComparisonMetricsRequest.type, fetchComparisonMetricsSaga),
    // Authentication metrics saga
    takeLatest(fetchAuthenticationMetricsRequest.type, fetchAuthenticationMetricsSaga),
  ]);
}
