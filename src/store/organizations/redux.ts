// Organizations Redux Store
import { createAction, createReducer } from "@reduxjs/toolkit";
import {
  Organization,
  CreateOrganizationData,
  UpdateOrganizationData,
  PaginationState,
  OrganizationFilterParams,
} from "../../types/organization";

// State Interface
export interface OrganizationsState {
  organizations: Organization[];
  currentOrganization: Organization | null;
  loading: boolean;
  createLoading: boolean;
  updateLoading: boolean;
  deleteLoading: boolean;
  error: string | null;
  pagination: PaginationState;
  searchQuery: string;
  industriesCount: number;
  filters: OrganizationFilterParams; // New filter state
}

// Initial State
const initialState: OrganizationsState = {
  organizations: [],
  currentOrganization: null,
  loading: false,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
  industriesCount: 0,
  searchQuery: "",
  filters: {
    page: 1,
    pageSize: 10,
    sortBy: "dateUpdated",
    sortOrder: "desc",
  },
};

// Actions
export const organizationsActions = {
  // List actions
  fetchOrganizations: createAction<OrganizationFilterParams>(
    "organizations/fetchOrganizations"
  ),
  fetchOrganizationsSuccess: createAction<{
    organizations: Organization[];
    pagination: PaginationState;
  }>("organizations/fetchOrganizationsSuccess"),
  fetchOrganizationsFailure: createAction<string>(
    "organizations/fetchOrganizationsFailure"
  ),

  // Single organization actions
  fetchOrganization: createAction<string>("organizations/fetchOrganization"),
  fetchOrganizationSuccess: createAction<Organization>(
    "organizations/fetchOrganizationSuccess"
  ),
  fetchOrganizationFailure: createAction<string>(
    "organizations/fetchOrganizationFailure"
  ),

  // Create/Update actions
  createOrganization: createAction<CreateOrganizationData>(
    "organizations/createOrganization"
  ),
  createOrganizationSuccess: createAction<Organization>(
    "organizations/createOrganizationSuccess"
  ),
  createOrganizationFailure: createAction<string>(
    "organizations/createOrganizationFailure"
  ),

  updateOrganization: createAction<{
    uuid: string;
    data: UpdateOrganizationData;
  }>("organizations/updateOrganization"),
  updateOrganizationSuccess: createAction<Organization>(
    "organizations/updateOrganizationSuccess"
  ),
  updateOrganizationFailure: createAction<string>(
    "organizations/updateOrganizationFailure"
  ),

  // Delete actions
  deleteOrganization: createAction<string>("organizations/deleteOrganization"),
  deleteOrganizationSuccess: createAction<string>(
    "organizations/deleteOrganizationSuccess"
  ),
  deleteOrganizationFailure: createAction<string>(
    "organizations/deleteOrganizationFailure"
  ),

  // Filter actions
  setFilters: createAction<OrganizationFilterParams>(
    "organizations/setFilters"
  ),
  updateFilters: createAction<Partial<OrganizationFilterParams>>(
    "organizations/updateFilters"
  ),
  clearFilters: createAction("organizations/clearFilters"),

  // UI actions
  setSearchQuery: createAction<string>("organizations/setSearchQuery"),
  setPageSize: createAction<number>("organizations/setPageSize"),
  setCurrentPage: createAction<number>("organizations/setCurrentPage"),
  setPagination: createAction<PaginationState>("organizations/setPagination"),
  clearError: createAction("organizations/clearError"),
  clearCurrentOrganization: createAction(
    "organizations/clearCurrentOrganization"
  ),
  industriesSuccess: createAction<number>("organizations/industriesSuccess"),

};

// Reducer
export const organizationsReducer = createReducer(initialState, (builder) => {
  builder
    // Fetch Organizations
    .addCase(organizationsActions.fetchOrganizations, (state) => {
      state.loading = true;
      state.error = null;
    })
    .addCase(
      organizationsActions.fetchOrganizationsSuccess,
      (state, action) => {
        state.loading = false;
        state.organizations = action.payload.organizations;
        state.pagination = action.payload.pagination;
      }
    )
    .addCase(
      organizationsActions.fetchOrganizationsFailure,
      (state, action) => {
        state.loading = false;
        state.error = action.payload;
      }
    )

    // Fetch Single Organization
    .addCase(organizationsActions.fetchOrganization, (state) => {
      state.loading = true;
      state.error = null;
    })
    .addCase(organizationsActions.fetchOrganizationSuccess, (state, action) => {
      state.loading = false;
      state.currentOrganization = action.payload;
    })
    .addCase(organizationsActions.fetchOrganizationFailure, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })

    // Create Organization
    .addCase(organizationsActions.createOrganization, (state) => {
      state.createLoading = true;
      state.error = null;
    })
    .addCase(
      organizationsActions.createOrganizationSuccess,
      (state, action) => {
        state.createLoading = false;
        state.organizations.unshift(action.payload);
      }
    )
    .addCase(
      organizationsActions.createOrganizationFailure,
      (state, action) => {
        state.createLoading = false;
        state.error = action.payload;
      }
    )

    // Update Organization
    .addCase(organizationsActions.updateOrganization, (state) => {
      state.updateLoading = true;
      state.error = null;
    })
    .addCase(
      organizationsActions.updateOrganizationSuccess,
      (state, action) => {
        state.updateLoading = false;
        const index = state.organizations.findIndex(
          (org) => org.uuid === action.payload.uuid
        );
        if (index !== -1) {
          state.organizations[index] = action.payload;
        }
        if (state.currentOrganization?.uuid === action.payload.uuid) {
          state.currentOrganization = action.payload;
        }
      }
    )
    .addCase(
      organizationsActions.updateOrganizationFailure,
      (state, action) => {
        state.updateLoading = false;
        state.error = action.payload;
      }
    )

    // Delete Organization
    .addCase(organizationsActions.deleteOrganization, (state) => {
      state.deleteLoading = true;
      state.error = null;
    })
    .addCase(
      organizationsActions.deleteOrganizationSuccess,
      (state, action) => {
        state.deleteLoading = false;
        state.organizations = state.organizations.filter(
          (org) => org.uuid !== action.payload
        );
        if (state.currentOrganization?.uuid === action.payload) {
          state.currentOrganization = null;
        }
      }
    )
    .addCase(
      organizationsActions.deleteOrganizationFailure,
      (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload;
      }
    )

    // Filter actions
    .addCase(organizationsActions.setFilters, (state, action) => {
      state.filters = action.payload;
    })
    .addCase(organizationsActions.updateFilters, (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    })
    .addCase(organizationsActions.clearFilters, (state) => {
      state.filters = initialState.filters;
    })

    // UI Actions
    .addCase(organizationsActions.setSearchQuery, (state, action) => {
      state.searchQuery = action.payload;
    })
    .addCase(organizationsActions.setPageSize, (state, action) => {
      state.pagination.pageSize = action.payload;
      state.pagination.page = 1; // Reset to first page when changing page size
    })
    .addCase(organizationsActions.setCurrentPage, (state, action) => {
      state.pagination.page = action.payload;
    })
    .addCase(organizationsActions.setPagination, (state, action) => {
      state.pagination = { ...action.payload };
    })
    .addCase(organizationsActions.clearError, (state) => {
      state.error = null;
    })
    .addCase(organizationsActions.clearCurrentOrganization, (state) => {
      state.currentOrganization = null;
    }).addCase(organizationsActions.industriesSuccess, (state, action) => {
      state.industriesCount = action.payload;
    });
});

export default organizationsReducer;
