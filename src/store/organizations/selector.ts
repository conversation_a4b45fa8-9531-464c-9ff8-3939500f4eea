import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "../index";
import { OrganizationsState } from "./redux";

// Base selectors
const selectOrganizationsState = (state: RootState) => state.organizations;
const selectOrganizations = (state: RootState) =>
  state.organizations.organizations;
const selectCurrentOrganization = (state: RootState) =>
  state.organizations.currentOrganization;
const selectLoading = (state: RootState) => state.organizations.loading;
const selectCreateLoading = (state: RootState) =>
  state.organizations.createLoading;
const selectUpdateLoading = (state: RootState) =>
  state.organizations.updateLoading;
const selectDeleteLoading = (state: RootState) =>
  state.organizations.deleteLoading;
const selectError = (state: RootState) => state.organizations.error;
const selectPagination = (state: RootState) => state.organizations.pagination;
const selectSearchQuery = (state: RootState) => state.organizations.searchQuery;
const selectFilters = (state: RootState) => state.organizations.filters;
const selectIndustriesCount = (state: RootState) =>
  state.organizations.industriesCount;
// State selectors
export const organizationsSelectors = {
  // Basic state selectors
  selectOrganizations,
  selectCurrentOrganization,
  selectLoading,
  selectCreateLoading,
  selectUpdateLoading,
  selectDeleteLoading,
  selectError,
  selectPagination,
  selectSearchQuery,
  selectFilters,
  selectIndustriesCount,
  // Derived selectors
  selectOrganizationsByIndustry: createSelector(
    [selectOrganizations],
    (organizations) => {
      const grouped = organizations.reduce((acc, org) => {
        const industry = org.industry || "Unknown";
        acc[industry] = (acc[industry] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      return grouped;
    }
  ),

  selectOrganizationSummary: createSelector(
    [selectOrganizations],
    (organizations) => {
      const summary = {
        total: organizations.length,
        withEmail: organizations.filter((org) => org.email).length,
        withPhone: organizations.filter((org) => org.phone).length,
        byIndustry: organizations.reduce((acc, org) => {
          const industry = org.industry || "Unknown";
          acc[industry] = (acc[industry] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
      };
      return summary;
    }
  ),

  // Total count selector
  selectTotalOrganizations: createSelector(
    [selectPagination],
    (pagination) => pagination.total
  ),

  // Loading state selectors
  selectIsLoading: createSelector(
    [
      selectLoading,
      selectCreateLoading,
      selectUpdateLoading,
      selectDeleteLoading,
    ],
    (loading, createLoading, updateLoading, deleteLoading) =>
      loading || createLoading || updateLoading || deleteLoading
  ),
};
