// Industry Types
export interface Industry {
  id: number;
  uuid: string;
  name: string;
  description?: string;
  created_at: string;
}

export interface IndustryCreate {
  name: string;
  description?: string;
}

export interface IndustryUpdate {
  name?: string;
  description?: string;
}

export interface IndustryResponse {
  id: number;
  uuid: string;
  name: string;
  description?: string;
  created_at: string;
}

export interface IndustriesResponse {
  industries: Industry[];
  total: number;
  page: number;
  size: number;
}
