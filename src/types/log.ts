// Log Types based on API Documentation

export interface LogEntry {
  id: number;
  uuid: string;
  timestamp: string;
  userUuid: string;
  username: string;
  action: string;
  filters_applied: Record<string, any>;
  selected_fields: string[];
  purpose: string;
  // Additional fields for member_delete actions
  deleted_email?: string;
  deleted_member_uuid?: string;
  auth0_id?: string;
}

export interface LogListResponse {
  status: string;
  message: string;

  logs: LogEntry[];
  total_count: number;
  page: number;
  page_size: number;
}

export interface LogFilterParams {
  start_timestamp?: string;
  end_timestamp?: string;
  purpose?: string;
  action?: string;
  action_exclude?: string | string[];
  page?: number;
  page_size?: number;
  limit?: number;
  sort_order?: "asc" | "desc";
}

export interface ExportField {
  field: string;
  display_name: string;
  description: string;
}

export interface ExportFieldsResponse {
  status: string;
  message: string;
  data: {
    fields: ExportField[];
  };
}

export interface ActionsResponse {
  status: string;
  message: string;
  data: {
    actions: string[];
  };
}

export interface ExportRequest {
  filters: Omit<LogFilterParams, "page" | "page_size">;
  selected_fields: string[];
  notes: string;
}

export interface ExportResponse {
  status: string;
  message: string;
  data: {
    csv_content: string; // Base64 encoded CSV
    filename: string;
    export_timestamp: string;
  };
}

export interface PaginationState {
  page: number;
  page_size: number;
  total_count: number;
}

// Page size options (maximum limit: 1000)
export const PAGE_SIZE_OPTIONS = [
  { value: 25, label: "25 per page" },
  { value: 50, label: "50 per page" },
  { value: 100, label: "100 per page" },
];

// Export limit options (maximum limit: 1000)
export const EXPORT_LIMIT_OPTIONS = [
  { value: 100, label: "100 records" },
  { value: 250, label: "250 records" },
  { value: 500, label: "500 records" },
  { value: 1000, label: "1000 records" },
];

// Available export fields
export const DEFAULT_EXPORT_FIELDS = [
  "timestamp",
  "username",
  "action",
  "deleted_email",
  "deleted_member_uuid",
  "auth0_id",
  "filters_applied",
  "selected_fields",
  "purpose",
];
