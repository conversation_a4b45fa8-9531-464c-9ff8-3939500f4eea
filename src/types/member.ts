// Main member interface
export interface Member {
  id: number;
  uuid: string;
  auth0Id: string;
  customerIoId?: string;
  openWaterId?: number;
  firstName?: string;
  lastName?: string;
  loginEmail?: string;
  loginEmailVerified: boolean;
  identityType?: "business" | "individual" | "organization";
  personalBusinessEmail?: string;
  phone?: string;
  professionalTitle?: string;
  membershipTier: "basic" | "premium" | "enterprise" | "vip";
  communityStatus: "unverified" | "verified" | "pending" | "rejected";
  verificationStatus?: "pending" | "verified" | "rejected" | "under_review";
  hasSeenhirstloginmessage: boolean;
  dateCreated: string;
  dateUpdated: string;
}

// Organization interface
export interface Organization {
  id: number;
  name?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  email?: string;
  annualRevenue?: string;
  industry?: string;
  yearFounded?: string;
  companySize?: string;
  businessProfileElementId?: number;
  dateCreated: string;
  dateUpdated: string;
}

// Verified data interfaces
export interface MemberVerifiedData {
  memberId: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  professionalTitle?: string;
  verificationStatus:
    | "requires_review"
    | "in_progress"
    | "completed"
    | "rejected";
  verificationType: "auto" | "manual";
  dateCreated: string;
  dateUpdated: string;
}

export interface OrganizationVerifiedData {
  organizationId: number;
  name?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  ein?: string;
  industry?: string;
  foundingYear?: number;
  verificationStatus:
    | "requires_review"
    | "in_progress"
    | "completed"
    | "rejected";
  verificationType: "auto" | "manual";
  dateCreated: string;
  dateUpdated: string;
}

// Awards interface - REMOVED: No longer used
// export interface MemberAward {
//   memberId: number;
//   organizationId: number;
//   awardListingElementId: number;
//   status: string;
//   progress?: number;
//   categories?: any;
//   isDisQualified: boolean;
//   isPreviousWinner: boolean;
//   isQualified: boolean;
//   isJudged?: boolean;
//   isPaid?: boolean;
//   isWinner?: boolean;
//   winnerTypes?: string;
//   applicationLink?: string;
//   startedDate?: string;
//   submittedDate?: string;
//   dateCreated: string;
//   dateUpdated: string;
// }

// Blacklist report interface
export interface MemberBlacklistReport {
  memberId: number;
  disposableemail: boolean;
  appears: boolean;
  frequency: number;
  submitted: string;
  updated: string;
  spamRate: number;
  exists?: boolean;
  inAntispamUpdated?: string;
  dateCreated: string;
  dateUpdated: string;
  isSpam: boolean;
}

// Feature flag interface - REMOVED: No longer used
// export interface MemberFeatureFlag {
//   id: number;
//   memberId: number;
//   featureHandle: string;
//   enabled: boolean;
// }

// Member with all relations
export interface MemberWithRelations extends Member {
  organizations: Organization[];
  verifiedData?: MemberVerifiedData;
  // awards: MemberAward[]; // REMOVED: No longer used
  blacklistReport?: MemberBlacklistReport;
  // featureFlags: MemberFeatureFlag[]; // REMOVED: No longer used
}

// Member with organizations (for API responses)
export interface MemberWithOrganizations extends Member {
  organizations: OrganizationInfo[];
  verifiedData?: MemberVerifiedData;
  // awards?: MemberAward[]; // REMOVED: No longer used
  blacklistReport?: MemberBlacklistReport;
  // featureFlags?: MemberFeatureFlag[]; // REMOVED: No longer used
}

// Legacy filters interface (for backward compatibility)
export interface MemberFilters {
  search: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  membershipTier?: string;
  communityStatus?: string;
  organizationName?: string;
  organizationCity?: string;
  organizationState?: string;
  organizationZip?: string;
  companySize?: string;
  industry?: string;
  // Legacy fields for backward compatibility
  status?: "all" | "active" | "inactive" | "pending";
  role?: "all" | "admin" | "member" | "premium";
  membershipType?: "all" | "basic" | "premium" | "enterprise";
  identityType?: "all" | "business" | "individual" | "organization";
}

// Search parameters interface
export interface MemberSearchParams {
  page: number;
  pageSize: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
  filters: MemberFilters;
}

// Bulk action interface
export interface MemberBulkAction {
  memberIds: string[];
  action: "delete" | "export" | "update_status" | "update_tier" | "verify";
  data?: any;
}

// Form types for creating/editing members
export interface CreateMemberData {
  firstName: string;
  lastName: string;
  loginEmail: string;
  phone?: string;
  professionalTitle?: string;
  membershipTier: "lite" | "premium";
  communityStatus: "unverified" | "verified" | "pending" | "rejected";
  verificationStatus?: "pending" | "verified" | "rejected" | "under_review";
  personalBusinessEmail?: string;
  password?: string;
  confirmPassword?: string;
  organization?: {
    name?: string;
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    zip?: string;
    phone?: string;
    email?: string;
    annualRevenue?: string;
    industry?: string;
    yearFounded?: string;
    companySize?: string;
  };
  selectedOrganizations?: Array<{
    uuid: string;
    name: string;
    city?: string;
    state?: string;
    industry?: string;
  }>;
}

export interface UpdateMemberData extends Partial<CreateMemberData> {
  // No uuid field needed since it's passed in the URL
}

// Verification request interface
export interface VerificationRequest {
  memberId: string;
  verificationType: "auto" | "manual";
  data: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    professionalTitle?: string;
  };
}

// API response interfaces
export interface MemberWithOrganizationsResponse {
  success: boolean;
  message: string;
  members: MemberWithOrganizations[];
  pagination: {
    totalCount: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

// Organization info interface
export interface OrganizationInfo {
  uuid: string;
  name?: string;
  phone?: string;
  email?: string;
  companySize?: string;
  city?: string;
  state?: string;
  zip?: string;
  industry?: string;
  dateCreated: string;
}

// Export interfaces
export interface MemberExportRequest {
  filters: {
    id?: number;
    uuid?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    organizationName?: string;
    membershipTier?: string;
    dateCreated?: string;
    city?: string;
    state?: string;
    zip?: string;
    industry?: string;
    companySize?: string;
    hasOrganizations?: boolean;
    nameFilter?: "all" | "exclude" | "include";
    // Additional filters for export
    search?: string;
    organizationCity?: string;
    organizationState?: string;
    organizationZip?: string;
    dateCreatedFrom?: string;
    dateCreatedTo?: string;
  };
  selectedFields: string[];
  notes: string;
  // Sorting parameters for export
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Export job interfaces for asynchronous export API
export interface ExportJob {
  job_id: string;
  file_name: string;
  status: 'PROCESSING' | 'COMPLETED' | 'FAILED';
  created_at: string;
  completed_at?: string;
  record_count?: number;
  download_url?: string;
  expires_at?: string;
  error_message?: string;
  file_size?: number;
}

export interface ExportJobStatus {
  job_id: string;
  file_name: string;
  status: 'PROCESSING' | 'COMPLETED' | 'FAILED';
  download_url?: string;
  expires_at?: string;
  record_count?: number;
  file_size?: number;
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

export interface ExportJobsList {
  jobs: ExportJob[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

export interface MemberExportResponse {
  success: boolean;
  job_id: string;
  status: string;
  message: string;
  file_name: string;
  created_by: string;
}

// Search filters interface
export interface MemberSearchFilters {
  search?: string;
  id?: number;
  uuid?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  membershipTier?: string;
  organizationName?: string;
  organizationCity?: string;
  organizationState?: string;
  organizationZip?: string;
  companySize?: string;
  industry?: string;
  dateCreatedFrom?: string;
  dateCreatedTo?: string;
  hasOrganizations?: boolean;
  nameFilter?: "all" | "exclude" | "include";
}

// Authentication metrics interface
export interface AuthenticationMetrics {
  totalMembers: number;
  usernamePasswordCount: number;
  appleCount: number;
  googleOAuth2Count: number;
  linkedinCount: number;
  otherCount: number;
  authenticationBreakdown: {
    "Username-Password-Authentication": number;
    apple: number;
    "google-oauth2": number;
    linkedin: number;
    other: number;
  };
}
