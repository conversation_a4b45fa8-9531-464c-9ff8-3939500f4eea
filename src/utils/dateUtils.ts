// Date range utilities for dashboard metrics

export interface DateRange {
  startDate: string | null;
  endDate: string | null;
  label: string;
}

export const getDateRanges = (): DateRange[] => {
  const now = new Date();

  // This month
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const thisMonthEnd = new Date(
    now.getFullYear(),
    now.getMonth() + 1,
    0,
    23,
    59,
    59
  );

  // Last month
  const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const lastMonthEnd = new Date(
    now.getFullYear(),
    now.getMonth(),
    0,
    23,
    59,
    59
  );

  // Last 7 days
  const sevenDaysAgo = new Date(now);
  sevenDaysAgo.setDate(now.getDate() - 7);
  const sevenDaysEnd = new Date(now);
  sevenDaysEnd.setHours(23, 59, 59);

  return [
    {
      startDate: thisMonthStart.toISOString(),
      endDate: thisMonthEnd.toISOString(),
      label: "This Month",
    },
    {
      startDate: lastMonthStart.toISOString(),
      endDate: lastMonthEnd.toISOString(),
      label: "Last Month",
    },
    {
      startDate: sevenDaysAgo.toISOString(),
      endDate: sevenDaysEnd.toISOString(),
      label: "Last 7 Days",
    },
    {
      startDate: null,
      endDate: null,
      label: "All Time",
    },
  ];
};

export const getDefaultDateRange = (): DateRange => {
  const ranges = getDateRanges();
  return ranges[0]; // This month is default
};

export const formatDateRangeLabel = (
  startDate: string | null,
  endDate: string | null
): string => {
  if (!startDate || !endDate) {
    return "All Time";
  }

  const start = new Date(startDate);
  const end = new Date(endDate);

  // If it's a month range
  if (
    start.getDate() === 1 &&
    end.getDate() ===
      new Date(end.getFullYear(), end.getMonth() + 1, 0).getDate()
  ) {
    return start.toLocaleDateString("en-US", {
      month: "short",
      year: "numeric",
    });
  }

  // If it's a week range
  const daysDiff = Math.ceil(
    (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
  );
  if (daysDiff <= 7) {
    return `Last ${daysDiff} Days`;
  }

  // Custom range
  return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
};
