/**
 * Utility functions for handling export operations
 */

/**
 * Checks if a download link has expired
 * @param expiresAt - The expiration timestamp string (in UTC)
 * @returns true if the link is expired, false if still valid
 */
export const isDownloadLinkExpired = (expiresAt?: string): boolean => {
  if (!expiresAt) {
    // If no expiration time is provided, assume it's valid
    return false;
  }

  try {
    // Parse the UTC expiration time
    // If the timestamp doesn't have Z suffix, treat it as UTC
    let expirationTime: Date;
    if (expiresAt.endsWith('Z')) {
      expirationTime = new Date(expiresAt);
    } else {
      // Treat as UTC if no timezone indicator
      expirationTime = new Date(expiresAt + 'Z');
    }
    
    // Get current local time
    const currentTime = new Date();
    
    // Compare UTC expiration time with current local time
    // Note: Date objects automatically handle timezone conversion when comparing
    // The link is expired only when current time is AFTER the expiration time
    return currentTime.getTime() > expirationTime.getTime();
  } catch (error) {
    console.error('Error parsing expiration time:', error);
    // If we can't parse the date, assume it's expired for safety
    return true;
  }
};

/**
 * Gets the time remaining until expiration in a human-readable format
 * @param expiresAt - The expiration timestamp string (in UTC)
 * @returns Formatted string showing time remaining or "Expired"
 */
export const getTimeUntilExpiration = (expiresAt?: string): string => {
  if (!expiresAt) {
    return 'No expiration';
  }

  try {
    // Parse the UTC expiration time
    // If the timestamp doesn't have Z suffix, treat it as UTC
    let expirationTime: Date;
    if (expiresAt.endsWith('Z')) {
      expirationTime = new Date(expiresAt);
    } else {
      // Treat as UTC if no timezone indicator
      expirationTime = new Date(expiresAt + 'Z');
    }
    
    // Get current local time
    const currentTime = new Date();
    
    // Calculate time remaining (Date objects handle timezone conversion automatically)
    const timeRemaining = expirationTime.getTime() - currentTime.getTime();

    if (timeRemaining <= 0) {
      return 'Expired';
    }

    // Convert to hours and minutes
    const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else if (minutes > 0) {
      return `${minutes}m remaining`;
    } else {
      return 'Less than 1m remaining';
    }
  } catch (error) {
    console.error('Error calculating time until expiration:', error);
    return 'Unknown';
  }
};

/**
 * Checks if a download link will expire soon (within 1 hour)
 * @param expiresAt - The expiration timestamp string (in UTC)
 * @returns true if the link expires within 1 hour
 */
export const isDownloadLinkExpiringSoon = (expiresAt?: string): boolean => {
  if (!expiresAt) {
    return false;
  }

  try {
    // Parse the UTC expiration time
    // If the timestamp doesn't have Z suffix, treat it as UTC
    let expirationTime: Date;
    if (expiresAt.endsWith('Z')) {
      expirationTime = new Date(expiresAt);
    } else {
      // Treat as UTC if no timezone indicator
      expirationTime = new Date(expiresAt + 'Z');
    }
    
    // Get current local time
    const currentTime = new Date();
    
    // Calculate time remaining (Date objects handle timezone conversion automatically)
    const timeRemaining = expirationTime.getTime() - currentTime.getTime();
    const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
    
    return timeRemaining <= oneHour && timeRemaining > 0;
  } catch (error) {
    console.error('Error checking if link expires soon:', error);
    return false;
  }
};
