// Utility function to convert camelCase to snake_case
export function camelToSnakeCase(str: string): string {
  return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
}


// Convert form data from camelCase to snake_case for API submission
export function convertMemberFormData(formData: any): any {
  return formData;
}

// Convert API response from snake_case to camelCase for form usage
export function convertSnakeToCamelCase(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => convertSnakeToCamelCase(item));
  }

  if (typeof obj === "object") {
    const converted: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) =>
        letter.toUpperCase()
      );
      converted[camelKey] = convertSnakeToCamelCase(value);
    }
    return converted;
  }

  return obj;
}
